#include "x86_64_jit_compiler.h"
#include "../common/lock_ordering.h"
#include "../cpu/decoded_instruction.h"
#include "../cpu/instruction_decoder.h"
#include "../cpu/x86_64_cpu.h"
#include "../debug/vector_debug.h"
#include "x86_64_jit_helpers.h"
#include <algorithm>
#include <chrono>
#include <cstring>
#include <emmintrin.h>
#include <immintrin.h>
#include <mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <unordered_set>
#include <vector>

using InstructionType = x86_64::InstructionType;

#ifdef _WIN32
#define WIN32_LEAN_AND_MEAN
#define NOMINMAX
#include <windows.h>
#else
#include <sys/mman.h>
#endif

namespace x86_64 {
struct JITCompileException : std::runtime_error {
  explicit JITCompileException(const std::string &msg)
      : std::runtime_error(msg) {}
};

static constexpr uint32_t SPILL_AREA_OFFSET = 0x100;

// Safe operand access helper implementation
const DecodedInstruction::Operand* X86_64JITCompiler::SafeGetOperand(const DecodedInstruction& instr, uint8_t index) const {
    if (index >= instr.operands.size()) {
        return nullptr;
    }
    return &instr.operands[index];
}

X86_64JITCompiler::X86_64JITCompiler(X86_64CPU *cpu) : m_cpu(cpu) {
  if (!cpu) {
    spdlog::error("JIT compiler: null CPU pointer");
    throw JITCompileException("Null CPU pointer");
  }

  m_stats = {};
  m_stats.blocksCompiled = 0;
  m_stats.executions = 0;
  m_stats.cacheClears = 0;
  m_stats.simdInstructions = 0;
  m_stats.compilationLatencyUs = 0;
  m_stats.cacheHits = 0;
  m_stats.cacheMisses = 0;

  spdlog::info(
      "JIT compiler initialized for CPU 0x{:x} with zero-initialized stats",
      reinterpret_cast<std::uintptr_t>(cpu));
}

X86_64JITCompiler::~X86_64JITCompiler() noexcept { ClearCache(); }

bool X86_64JITCompiler::IsAvailable() const {
#ifdef _WIN32
  SYSTEM_INFO sysInfo;
  GetSystemInfo(&sysInfo);
  return sysInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_AMD64;
#else
  return true;
#endif
}

bool X86_64JITCompiler::CompileBlock(uint64_t pc) {
  auto start = std::chrono::steady_clock::now();
  JIT_LOCK(m_cacheMutex);
  try {
    if (m_compiledBlocks.find(pc) != m_compiledBlocks.end()) {
      m_compiledBlocks[pc].lastUsed = m_cycleCount++;
      m_stats.cacheHits++;
      spdlog::trace("Reusing cached JIT block at 0x{:x}", pc);
      return true;
    }
    m_stats.cacheMisses++;

    InstructionDecoder decoder;
    std::vector<DecodedInstruction> instructions;
    uint64_t currentPc = pc;
    size_t instructionCount = 0;
    constexpr size_t MAX_INSTRUCTIONS = 64;

    while (instructionCount < MAX_INSTRUCTIONS) {
      std::vector<uint8_t> buffer(16);
      try {
        if (currentPc == 0 || currentPc == UINT64_MAX) {
          spdlog::error("JIT compile: invalid PC address 0x{:x}", currentPc);
          return false;
        }
        m_cpu->GetMemory().ReadVirt(currentPc, buffer.data(), buffer.size(),
                                    m_cpu->GetProcessId());
        DecodedInstruction instr;
        DecoderErrorInfo error =
            decoder.Decode(currentPc, buffer.data(), buffer.size(), instr);
        if (error.error != DecoderError::Success) {
          spdlog::error("JIT decode failed at 0x{:x}: {}", currentPc,
                        error.message);
          return false;
        }
        instructions.push_back(instr);
        currentPc += instr.length;
        instructionCount++;
        if (instr.instType == InstructionType::Jump ||
            instr.instType == InstructionType::Ret ||
            instr.instType == InstructionType::Call ||
            instr.instType == InstructionType::Int ||
            instr.instType == InstructionType::Iret ||
            instr.instType == InstructionType::Jcc) {
          break;
        }
      } catch (const std::exception &e) {
        spdlog::error("JIT fetch failed at 0x{:x}: {}", currentPc, e.what());
        return false;
      }
    }

    std::vector<uint8_t> code;
    AllocateRegisters(instructions);

    code.insert(code.end(), {
                                0x53,       // push rbx
                                0x41, 0x54, // push r12
                                0x41, 0x55, // push r13
                                0x41, 0x56, // push r14
                                0x55        // push rbp
                            });

    for (size_t i = 0; i < instructions.size(); ++i) {
      // CRITICAL: Add bounds check for instruction access
      const auto &instr = SAFE_VECTOR_ACCESS(instructions, i, "JIT instruction compilation");
      uint64_t nextRip = currentPc + instr.length;
      switch (instr.instType) {
      case InstructionType::Nop:
        code.push_back(0x90);
        break;
      case InstructionType::Mov:
        EmitMovInstruction(instr, code);
        break;
      case InstructionType::Add:
        EmitAddInstruction(instr, code);
        break;
      case InstructionType::Sub:
        EmitSubInstruction(instr, code);
        break;
      case InstructionType::Push:
        EmitPushInstruction(instr, code);
        break;
      case InstructionType::Pop:
        EmitPopInstruction(instr, code);
        break;
      case InstructionType::Addps:
      case InstructionType::Subps:
      case InstructionType::Mulps:
        if (m_simdOptimizationsEnabled) {
          m_stats.simdInstructions++;
          if (!CompileSIMD(instr, code)) {
            spdlog::error("Failed to compile SIMD instruction {} at 0x{:x}",
                          static_cast<int>(instr.instType), pc);
            return false;
          }
        } else {
          spdlog::warn("SIMD instruction {} at 0x{:x} ignored (SIMD "
                       "optimizations disabled)",
                       static_cast<int>(instr.instType), pc);
          return false;
        }
        break;
      default:
        spdlog::error("Unsupported JIT instruction {} at 0x{:x}",
                      static_cast<int>(instr.instType), pc);
        return false;
      }
    }

    code.insert(code.end(), {
                                0x5D,       // pop rbp
                                0x41, 0x5E, // pop r14
                                0x41, 0x5D, // pop r13
                                0x41, 0x5C, // pop r12
                                0x5B,       // pop rbx
                                0xC3        // ret
                            });

    OptimizeBlock(code);

    if (code.empty()) {
      spdlog::error("JIT compile: generated code is empty for block at 0x{:x}",
                    pc);
      return false;
    }

    constexpr size_t MIN_CODE_SIZE = 16;
    constexpr size_t MAX_BLOCK_SIZE = 1024 * 1024;
    constexpr size_t WARN_SIZE = 64 * 1024;

    if (code.size() < MIN_CODE_SIZE) {
      spdlog::error("JIT compile: generated code too small ({} bytes) for "
                    "block at 0x{:x}, minimum {} bytes",
                    code.size(), pc, MIN_CODE_SIZE);
      return false;
    }

    if (code.size() > MAX_BLOCK_SIZE) {
      spdlog::error("JIT compile: generated code too large ({} bytes) for "
                    "block at 0x{:x}, maximum {} bytes",
                    code.size(), pc, MAX_BLOCK_SIZE);
      return false;
    }

    if (code.size() > WARN_SIZE) {
      spdlog::warn("JIT compile: large code block ({} bytes) at 0x{:x}, "
                   "consider optimization",
                   code.size(), pc);
    }

    size_t instructionBytes = 0;
    for (const auto &instr : instructions) {
      instructionBytes += instr.length;
    }

    if (code.size() > instructionBytes * 10) {
      spdlog::warn(
          "JIT compile: generated code size ({} bytes) seems "
          "disproportionately large compared to source ({} bytes) at 0x{:x}",
          code.size(), instructionBytes, pc);
    }

    uint8_t *executableCode = AllocateExecutableMemory(code.size());
    if (!executableCode) {
      spdlog::error("JIT compile: failed to allocate executable memory for "
                    "block at 0x{:x}",
                    pc);
      return false;
    }
    // CRITICAL: Add bounds check before memcpy
    if (executableCode == nullptr) {
      spdlog::error("JIT compile: executable code pointer is null");
      return false;
    }
    if (code.empty()) {
      spdlog::error("JIT compile: code vector is empty");
      return false;
    }
    if (code.size() > size) {
      spdlog::error("JIT compile: code size {} exceeds allocated size {}",
                    code.size(), size);
      return false;
    }
    // Additional safety check for code data pointer
    if (code.data() == nullptr) {
      spdlog::error("JIT compile: code data pointer is null");
      return false;
    }
    std::memcpy(executableCode, code.data(), code.size());

    FixupRelativeAddresses(executableCode, code.size());

    m_totalCodeSize += code.size();
    if (m_totalCodeSize > MAX_CODE_SIZE) {
      ClearCache();
    }

    auto end = std::chrono::steady_clock::now();
    uint64_t compilationTimeUs =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.compilationLatencyUs += compilationTimeUs;

    uint32_t branchCount = static_cast<uint32_t>(std::count_if(
        instructions.begin(), instructions.end(), [](const auto &i) {
          return i.instType == InstructionType::Jump ||
                 i.instType == InstructionType::Jcc ||
                 i.instType == InstructionType::Call ||
                 i.instType == InstructionType::Ret;
        }));

    uint64_t estimatedPipelineLatency =
        instructions.size() * 2 + instructions.size() + branchCount * 3;

    m_compiledBlocks[pc] = {
        executableCode,           code.size(), m_cycleCount++,   branchCount, 0,
        estimatedPipelineLatency, false,       compilationTimeUs};
    m_stats.blocksCompiled++;
    spdlog::info(
        "Compiled JIT block at 0x{:x}, size={} bytes, branches={}, time={}us",
        pc, code.size(), m_compiledBlocks[pc].branchCount, compilationTimeUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("JIT compilation failed at 0x{:x}: {}", pc, e.what());
    return false;
  }
}

bool X86_64JITCompiler::ExecuteCompiledCode(uint64_t pc) {
  auto start = std::chrono::steady_clock::now();
  JIT_LOCK(m_cacheMutex);
  try {
    auto it = m_compiledBlocks.find(pc);
    if (it == m_compiledBlocks.end() || !it->second.code) {
      m_stats.cacheMisses++;
      return false;
    }
    m_stats.cacheHits++;
    using JitFunc = void (*)(X86_64CPU *);
    JitFunc func = reinterpret_cast<JitFunc>(it->second.code);
    m_stats.executions++;
    it->second.executionCount++;
    func(m_cpu);
    auto end = std::chrono::steady_clock::now();
    uint64_t executionTimeUs =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    it->second.totalCycles += executionTimeUs;
    spdlog::trace("Executed JIT block at 0x{:x}, time={}us", pc,
                  executionTimeUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("JIT execution failed at 0x{:x}: {}", pc, e.what());
    return false;
  }
}

void X86_64JITCompiler::ClearCache() {
  JIT_LOCK(m_cacheMutex);
  try {
    for (auto &entry : m_compiledBlocks) {
      FreeExecutableMemory(entry.second.code, entry.second.size);
    }
    m_compiledBlocks.clear();
    m_totalCodeSize = 0;
    m_stats.cacheClears++;
    spdlog::info("JIT cache cleared");
  } catch (const std::exception &e) {
    spdlog::error("JIT cache clear failed: {}", e.what());
  }
}

void X86_64JITCompiler::InvalidateBlock(uint64_t pc) {
  JIT_LOCK(m_cacheMutex);
  try {
    auto it = m_compiledBlocks.find(pc);
    if (it != m_compiledBlocks.end()) {
      FreeExecutableMemory(it->second.code, it->second.size);
      m_totalCodeSize -= it->second.size;
      m_compiledBlocks.erase(it);
      spdlog::trace("Invalidated JIT block at 0x{:x}", pc);
    }
  } catch (const std::exception &e) {
    spdlog::error("JIT block invalidation failed at 0x{:x}: {}", pc, e.what());
  }
}

void X86_64JITCompiler::InvalidateRange(uint64_t addr, size_t size) {
  JIT_LOCK(m_cacheMutex);
  try {
    uint64_t endAddr = addr + size;
    auto it = m_compiledBlocks.begin();
    while (it != m_compiledBlocks.end()) {
      uint64_t blockAddr = it->first;
      if (blockAddr >= addr && blockAddr < endAddr) {
        FreeExecutableMemory(it->second.code, it->second.size);
        m_totalCodeSize -= it->second.size;
        it = m_compiledBlocks.erase(it);
        spdlog::trace("Invalidated JIT block at 0x{:x} in range", blockAddr);
      } else {
        ++it;
      }
    }
    spdlog::trace("Invalidated JIT blocks in range 0x{:x}-0x{:x}", addr,
                  endAddr);
  } catch (const std::exception &e) {
    spdlog::error("JIT range invalidation failed at 0x{:x}: {}", addr,
                  e.what());
  }
}

void X86_64JITCompiler::ProfileBlock(uint64_t pc, uint64_t cycles) {
  JIT_LOCK(m_cacheMutex);
  auto it = m_compiledBlocks.find(pc);
  if (it != m_compiledBlocks.end()) {
    it->second.executionCount++;
    it->second.totalCycles += cycles;
    if (it->second.executionCount > 1000) {
      it->second.isHotPath = true;
    }
    spdlog::trace("Profiled JIT block at 0x{:x}, executions={}, cycles={}", pc,
                  it->second.executionCount, cycles);
  }
}

bool X86_64JITCompiler::RecompileHotBlock(uint64_t pc, int optimizationLevel) {
  JIT_LOCK(m_cacheMutex);
  try {
    InvalidateBlock(pc);
    bool success = CompileBlock(pc);
    if (success && m_tieredCompilationEnabled) {
      auto it = m_compiledBlocks.find(pc);
      if (it != m_compiledBlocks.end()) {
        it->second.isHotPath = true;
        spdlog::info(
            "Recompiled hot JIT block at 0x{:x}, optimization level={}", pc,
            optimizationLevel);
      }
    }
    return success;
  } catch (const std::exception &e) {
    spdlog::error("Hot block recompilation failed at 0x{:x}: {}", pc, e.what());
    return false;
  }
}

float X86_64JITCompiler::GetCacheUsage() const {
  JIT_LOCK(m_cacheMutex);
  return static_cast<float>(m_totalCodeSize) / MAX_CODE_SIZE;
}

std::vector<std::pair<uint64_t, uint32_t>>
X86_64JITCompiler::GetHotBlocks(size_t count) const {
  JIT_LOCK(m_cacheMutex);
  std::vector<std::pair<uint64_t, uint32_t>> hotBlocks;
  for (const auto &entry : m_compiledBlocks) {
    if (entry.second.isHotPath) {
      hotBlocks.emplace_back(entry.first, entry.second.executionCount);
    }
  }
  std::sort(hotBlocks.begin(), hotBlocks.end(),
            [](const auto &a, const auto &b) { return a.second > b.second; });
  if (hotBlocks.size() > count) {
    hotBlocks.resize(count);
  }
  return hotBlocks;
}

uint8_t *X86_64JITCompiler::AllocateExecutableMemory(size_t size) {
  try {
    if (size == 0) {
      spdlog::error("AllocateExecutableMemory: invalid size 0");
      return nullptr;
    }
    if (size > MAX_CODE_SIZE) {
      spdlog::error("AllocateExecutableMemory: size {} exceeds maximum {}",
                    size, MAX_CODE_SIZE);
      return nullptr;
    }

    constexpr size_t MIN_ALLOC_SIZE = 16;
    constexpr size_t MAX_SINGLE_ALLOC = 2 * 1024 * 1024;

    if (size < MIN_ALLOC_SIZE) {
      spdlog::error("AllocateExecutableMemory: size {} below minimum {}", size,
                    MIN_ALLOC_SIZE);
      return nullptr;
    }

    if (size > MAX_SINGLE_ALLOC) {
      spdlog::error("AllocateExecutableMemory: size {} exceeds single "
                    "allocation limit {}",
                    size, MAX_SINGLE_ALLOC);
      return nullptr;
    }

    const size_t pageSize = 4096;
    size_t alignedSize = (size + pageSize - 1) & ~(pageSize - 1);

    if (alignedSize < size) {
      spdlog::error(
          "AllocateExecutableMemory: size alignment overflow for size {}",
          size);
      return nullptr;
    }

    if (alignedSize >= 64 * 1024) {
      const size_t largePageSize = 64 * 1024;
      alignedSize = (size + largePageSize - 1) & ~(largePageSize - 1);
      if (alignedSize < size) {
        spdlog::error("AllocateExecutableMemory: large page alignment overflow "
                      "for size {}",
                      size);
        return nullptr;
      }
    }

#ifdef _WIN32
    void *mem = VirtualAlloc(nullptr, alignedSize, MEM_COMMIT | MEM_RESERVE,
                             PAGE_EXECUTE_READWRITE);
    if (!mem) {
      DWORD error = GetLastError();
      spdlog::error("VirtualAlloc failed with error {} for size {}", error,
                    alignedSize);
      return nullptr;
    }

    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(mem, &mbi, sizeof(mbi)) == sizeof(mbi)) {
      if (mbi.State != MEM_COMMIT || mbi.Protect != PAGE_EXECUTE_READWRITE) {
        spdlog::error("VirtualAlloc returned memory with incorrect properties");
        VirtualFree(mem, 0, MEM_RELEASE);
        return nullptr;
      }
    }

    spdlog::trace(
        "Allocated executable memory: size={}, aligned={}, addr=0x{:x}", size,
        alignedSize, reinterpret_cast<uintptr_t>(mem));
    return static_cast<uint8_t *>(mem);
#else
    void *mem = mmap(nullptr, alignedSize, PROT_READ | PROT_WRITE | PROT_EXEC,
                     MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
    if (mem == MAP_FAILED) {
      spdlog::error("mmap failed with errno {} for size {}", errno,
                    alignedSize);
      return nullptr;
    }

    uintptr_t addr = reinterpret_cast<uintptr_t>(mem);
    if ((addr & (pageSize - 1)) != 0) {
      spdlog::error("mmap returned unaligned memory: addr=0x{:x}, pageSize={}",
                    addr, pageSize);
      munmap(mem, alignedSize);
      return nullptr;
    }

    spdlog::trace(
        "Allocated executable memory: size={}, aligned={}, addr=0x{:x}", size,
        alignedSize, addr);
    return static_cast<uint8_t *>(mem);
#endif
  } catch (const std::exception &e) {
    spdlog::error("Memory allocation failed: {}", e.what());
    return nullptr;
  }
}

void X86_64JITCompiler::FreeExecutableMemory(uint8_t *ptr, size_t size) {
  try {
    if (!ptr) {
      spdlog::warn("FreeExecutableMemory: null pointer passed");
      return;
    }
    if (size == 0) {
      spdlog::warn("FreeExecutableMemory: zero size passed for ptr 0x{:x}",
                   reinterpret_cast<uintptr_t>(ptr));
      return;
    }

    const size_t pageSize = 4096;
    size_t alignedSize = (size + pageSize - 1) & ~(pageSize - 1);

#ifdef _WIN32
    if (!VirtualFree(ptr, 0, MEM_RELEASE)) {
      DWORD error = GetLastError();
      spdlog::error("VirtualFree failed with error {} for ptr 0x{:x}", error,
                    reinterpret_cast<uintptr_t>(ptr));
    }
#else
    if (munmap(ptr, alignedSize) != 0) {
      spdlog::error("munmap failed with errno {} for ptr 0x{:x}", errno,
                    reinterpret_cast<uintptr_t>(ptr));
    }
#endif
  } catch (const std::exception &e) {
    spdlog::error("Memory free failed: {}", e.what());
  }
}

void X86_64JITCompiler::EmitLoadOperand(
    const DecodedInstruction::Operand &operand, uint8_t scratchReg,
    uint64_t nextRip, std::vector<uint8_t> &code) {
  try {
    switch (operand.type) {
    case DecodedInstruction::Operand::Type::IMMEDIATE: {
      code.insert(code.end(),
                  {0x48, static_cast<uint8_t>(0xB8 | (scratchReg & 0x7))});
      for (uint8_t i = 0; i < 8; ++i) {
        code.push_back((operand.immediate >> (8 * i)) & 0xFF);
      }
      break;
    }
    case DecodedInstruction::Operand::Type::REGISTER: {
      uint32_t offset = offsetof(CPUContext, registers) +
                        static_cast<uint32_t>(operand.reg) * 8;
      code.insert(
          code.end(),
          {0x48, 0x8B, static_cast<uint8_t>(0x80 | (scratchReg & 0x7))});
      code.push_back(offset & 0xFF);
      code.push_back((offset >> 8) & 0xFF);
      code.push_back((offset >> 16) & 0xFF);
      code.push_back((offset >> 24) & 0xFF);
      break;
    }
    case DecodedInstruction::Operand::Type::MEMORY: {
      uint64_t addr =
          operand.memory.base == Register::NONE ? operand.immediate : 0;
      code.insert(code.end(), {0x48, 0xBA});
      for (int i = 0; i < 8; ++i) {
        code.push_back((addr >> (8 * i)) & 0xFF);
      }
      code.insert(code.end(), {0x4C, static_cast<uint8_t>(0x89), 0xC0});
      auto helper = operand.size == 1   ? (void *)&jitReadMem8
                    : operand.size == 2 ? (void *)&jitReadMem16
                    : operand.size == 4 ? (void *)&jitReadMem32
                                        : (void *)&jitReadMem64;
      code.push_back(0xE8);
      size_t callInstrOffset = code.size() - 1;
      code.insert(code.end(), 4, 0x00);
      *reinterpret_cast<void **>(&code[code.size() - 4]) = helper;
      break;
    }
    default:
      throw JITCompileException("Unsupported operand for load");
    }
  } catch (const std::exception &e) {
    spdlog::error("EmitLoadOperand failed: {}", e.what());
    throw JITCompileException("Operand load emission failure");
  }
}

void X86_64JITCompiler::EmitStoreOperand(
    const DecodedInstruction::Operand &operand, uint8_t scratchReg,
    uint64_t nextRip, std::vector<uint8_t> &code) {
  try {
    switch (operand.type) {
    case DecodedInstruction::Operand::Type::REGISTER: {
      uint32_t offset = offsetof(CPUContext, registers) +
                        static_cast<uint32_t>(operand.reg) * 8;
      code.insert(
          code.end(),
          {0x48, 0x89, static_cast<uint8_t>(0x80 | (scratchReg & 0x7))});
      code.push_back(offset & 0xFF);
      code.push_back((offset >> 8) & 0xFF);
      code.push_back((offset >> 16) & 0xFF);
      code.push_back((offset >> 24) & 0xFF);
      break;
    }
    case DecodedInstruction::Operand::Type::MEMORY: {
      uint64_t addr =
          operand.memory.base == Register::NONE ? operand.immediate : 0;
      code.insert(code.end(), {0x48, 0xBA});
      for (int i = 0; i < 8; ++i) {
        code.push_back((addr >> (8 * i)) & 0xFF);
      }
      code.insert(
          code.end(),
          {0x4C, static_cast<uint8_t>(0x89 | ((scratchReg & 0x7) << 3)), 0xC0});
      auto helper = operand.size == 1   ? (void *)&jitWriteMem8
                    : operand.size == 2 ? (void *)&jitWriteMem16
                    : operand.size == 4 ? (void *)&jitWriteMem32
                                        : (void *)&jitWriteMem64;
      code.push_back(0xE8);
      size_t callInstrOffset = code.size() - 1;
      code.insert(code.end(), 4, 0x00);
      *reinterpret_cast<void **>(&code[code.size() - 4]) = helper;
      break;
    }
    default:
      throw JITCompileException("Unsupported operand for store");
    }
  } catch (const std::exception &e) {
    spdlog::error("EmitStoreOperand failed: {}", e.what());
    throw JITCompileException("Operand store emission failure");
  }
}

bool X86_64JITCompiler::CompileSIMD(const DecodedInstruction &instr,
                                    std::vector<uint8_t> &code) {
  try {
    if (instr.operandCount < 2) {
      spdlog::error("SIMD instruction {} requires at least 2 operands, got {}",
                    static_cast<int>(instr.instType), instr.operandCount);
      return false;
    }

    uint32_t sizeBits = instr.operands[0].size;
    uint32_t sizeBytes = sizeBits / 8;

    if (sizeBits != 128 && sizeBits != 256 && sizeBits != 16 &&
        sizeBits != 32) {
      spdlog::error("Invalid SIMD instruction size: {} bits ({} bytes) for "
                    "instruction {}",
                    sizeBits, sizeBytes, static_cast<int>(instr.instType));
      return false;
    }

    if (sizeBits == 16)
      sizeBits = 128;
    if (sizeBits == 32)
      sizeBits = 256;
    sizeBytes = sizeBits / 8;

    void *helper = nullptr;
    if (instr.instType == InstructionType::Addps) {
      helper = (void *)&jitAddps;
    } else if (instr.instType == InstructionType::Subps) {
      helper = (void *)&jitSubps;
    } else if (instr.instType == InstructionType::Mulps) {
      helper = (void *)&jitMulps;
    } else {
      spdlog::error("Unsupported SIMD instruction type: {}",
                    static_cast<int>(instr.instType));
      return false;
    }

    code.push_back(0xE8);
    size_t callInstrOffset = code.size() - 1;
    code.insert(code.end(), 4, 0x00);
    *reinterpret_cast<void **>(&code[code.size() - 4]) = helper;

    spdlog::trace("Compiled SIMD instruction {} with size {} bits",
                  static_cast<int>(instr.instType), sizeBits);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SIMD compilation failed: {}", e.what());
    return false;
  }
}

void X86_64JITCompiler::AllocateRegisters(
    std::vector<DecodedInstruction> &instructions) {
  if (instructions.empty()) {
    spdlog::trace("AllocateRegisters: no instructions to process");
    return;
  }

  try {
    m_registerStates.clear();
    m_registerStates.resize(NUM_PHYSICAL_REGS);
    for (auto &state : m_registerStates) {
      state.isAvailable = true;
      state.lastUsed = 0;
      state.boundVReg = Register::NONE;
    }

    std::vector<LiveRange> liveRanges;
    PerformLiveRangeAnalysis(instructions, liveRanges);

    if (!LinearScanRegisterAllocation(liveRanges)) {
      spdlog::warn("Register allocation failed, falling back to spilling");
    }

    spdlog::trace("Allocated registers for {} instructions with {} live ranges",
                  instructions.size(), liveRanges.size());

  } catch (const std::exception &e) {
    spdlog::error("Register allocation failed: {}", e.what());

    std::unordered_map<Register, uint8_t> regMap;
    uint8_t nextScratch = 0;
    for (auto &instr : instructions) {
      for (auto &operand : instr.operands) {
        if (operand.type == DecodedInstruction::Operand::Type::REGISTER &&
            regMap.find(operand.reg) == regMap.end()) {
          regMap[operand.reg] = nextScratch++;
          if (nextScratch > 3) {
            nextScratch = 0;
          }
        }
      }
    }
    spdlog::trace("Used fallback allocation for {} registers", regMap.size());
  }
}

void X86_64JITCompiler::PerformLiveRangeAnalysis(
    const std::vector<DecodedInstruction> &instructions,
    std::vector<LiveRange> &liveRanges) {
  if (instructions.empty()) {
    spdlog::trace("PerformLiveRangeAnalysis: no instructions to analyze");
    return;
  }

  try {
    std::unordered_map<Register, LiveRange *> activeRanges;
    std::unordered_map<Register, uint32_t> lastUse;
    std::unordered_map<Register, uint32_t> useCount;
    std::unordered_set<Register> loopRegisters;

    // First pass: collect use counts and last uses
    for (uint32_t i = 0; i < instructions.size(); ++i) {
      const auto &instr = instructions[i];

      if (instr.instType == InstructionType::Loop ||
          instr.instType == InstructionType::Loope ||
          instr.instType == InstructionType::Loopne ||
          (instr.instType == InstructionType::Jcc && instr.operandCount > 0 &&
           instr.operands[0].type ==
               DecodedInstruction::Operand::Type::IMMEDIATE &&
           static_cast<int32_t>(instr.operands[0].immediate) < 0)) {
        for (uint32_t j = (std::max)(0, static_cast<int32_t>(i) - 10); j <= i;
             ++j) {
          for (const auto &operand : instructions[j].operands) {
            if (operand.type == DecodedInstruction::Operand::Type::REGISTER) {
              loopRegisters.insert(operand.reg);
            }
          }
        }
      }

      for (const auto &operand : instr.operands) {
        if (operand.type == DecodedInstruction::Operand::Type::REGISTER &&
            operand.reg != Register::NONE) {
          useCount[operand.reg]++;
          lastUse[operand.reg] = i;
        }
      }
    }

    // Second pass: create live ranges
    for (uint32_t i = 0; i < instructions.size(); ++i) {
      const auto &instr = instructions[i];

        for (uint32_t opIdx = 0; opIdx < instr.operandCount; ++opIdx) {
          const auto &operand = instr.operands[opIdx];

          if (operand.type != DecodedInstruction::Operand::Type::REGISTER ||
              operand.reg == Register::NONE) {
            continue;
          }

          Register reg = operand.reg;

          if (reg == Register::RSP || reg == Register::RBP ||
              reg == Register::RIP) {
            continue;
          }

          auto it = activeRanges.find(reg);
          if (it != activeRanges.end()) {
            it->second->end = i;
            if (opIdx == 0) {
              it->second->isDestination = true;
            }
            it->second->accessCount++;
          } else {
            LiveRange range;
            range.start = i;
            range.end = lastUse[reg];
            range.virtualReg = reg;
            range.physicalReg = 0xFF;
            range.isSpilled = false;
            range.spillSlot = 0;
            range.isDestination = (opIdx == 0);
            range.accessCount = 1;

            float basePriority =
                static_cast<float>(useCount[reg]) / instructions.size();

            if (useCount[reg] > instructions.size() / 4) {
              basePriority *= 2.0f;
            }

            if (loopRegisters.count(reg)) {
              basePriority *= 3.0f;
            }

            uint32_t rangeLength = range.end - range.start + 1;
            if (rangeLength > instructions.size() / 2) {
              basePriority *= 1.5f;
            }

            if (range.isDestination) {
              basePriority *= 1.2f;
            }

            switch (reg) {
            case Register::RAX:
            case Register::RCX:
            case Register::RDX:
            case Register::RBX:
              basePriority *= 1.3f;
              break;
            case Register::RSI:
            case Register::RDI:
              basePriority *= 1.1f;
              break;
            default:
              break;
            }

            range.priority = std::min(basePriority, 10.0f);

            liveRanges.push_back(range);
            activeRanges[reg] = &liveRanges.back();

            spdlog::trace("Created live range for register {} (range {}-{}, "
                          "priority {:.2f})",
                          static_cast<int>(reg), range.start, range.end,
                          range.priority);
          }
        }
      }

      for (auto &range : liveRanges) {
        range.interferenceCount = 0;

        for (const auto &other : liveRanges) {
          if (&range != &other &&
              !(range.end < other.start || other.end < range.start)) {
            range.interferenceCount++;
          }
        }

        if (range.interferenceCount > 5) {
          range.priority *= 0.8f;
        }
      }

      std::sort(liveRanges.begin(), liveRanges.end(),
                [](const LiveRange &a, const LiveRange &b) {
                  if (a.start != b.start) {
                    return a.start < b.start;
                  }
                  return a.priority > b.priority;
                });

      spdlog::info("Live range analysis complete: {} ranges identified for {} "
                   "instructions",
                   liveRanges.size(), instructions.size());

      for (const auto &range : liveRanges) {
        if (range.priority >= 2.0f) {
          spdlog::debug(
              "High-priority range: register {} (range {}-{}, priority "
              "{:.2f}, interference {})",
              static_cast<int>(range.virtualReg), range.start, range.end,
              range.priority, range.interferenceCount);
        }
      }
    }
    catch (const std::exception &e) {
      spdlog::error("Live range analysis failed: {}", e.what());
      liveRanges.clear();
    }
  }

  bool X86_64JITCompiler::LinearScanRegisterAllocation(std::vector<LiveRange> &
                                                       liveRanges) {
    if (liveRanges.empty()) {
      spdlog::trace("LinearScanRegisterAllocation: no ranges to allocate");
      return true;
    }

    try {
      std::vector<LiveRange *> active;
      std::vector<LiveRange *> inactive;
      bool allAllocated = true;

      constexpr uint8_t TOTAL_REGS = NUM_PHYSICAL_REGS;
      constexpr uint8_t RESERVED_REGS = 6;
      constexpr uint8_t USABLE_REGS = TOTAL_REGS - RESERVED_REGS;

      const std::array<uint8_t, USABLE_REGS> regOrder = {0, 1, 2,  6,  7,
                                                         8, 9, 10, 11, 3};

      std::array<bool, TOTAL_REGS> regAvailable;
      std::array<uint32_t, TOTAL_REGS> regLastUsed;
      std::array<float, TOTAL_REGS> regPriority;

      regAvailable.fill(true);
      regLastUsed.fill(0);
      regPriority.fill(0.0f);

      regAvailable[4] = false;  // RSP
      regAvailable[5] = false;  // RBP
      regAvailable[12] = false; // R12
      regAvailable[13] = false; // R13
      regAvailable[14] = false; // R14
      regAvailable[15] = false; // R15

      uint32_t spillCount = 0;
      uint32_t maxActiveRanges = 0;

      for (auto &range : liveRanges) {
        uint32_t currentTime = range.start;

        auto activeEnd =
            std::remove_if(active.begin(), active.end(),
                           [currentTime](const LiveRange *activeRange) {
                             return activeRange->end < currentTime;
                           });

        for (auto it = activeEnd; it != active.end(); ++it) {
          if ((*it)->physicalReg != 0xFF) {
            regAvailable[(*it)->physicalReg] = true;
            regPriority[(*it)->physicalReg] = 0.0f;
            spdlog::trace("Freed register {} from range ending at {}",
                          (*it)->physicalReg, (*it)->end);
          }
        }
        active.erase(activeEnd, active.end());

        maxActiveRanges =
            std::max(maxActiveRanges, static_cast<uint32_t>(active.size()));

        uint8_t allocatedReg = 0xFF;
        float bestScore = -1.0f;

        for (uint8_t regIdx : regOrder) {
          if (regAvailable[regIdx]) {
            float score = 1.0f;
            if (regLastUsed[regIdx] < currentTime - 5) {
              score += 0.5f;
            }
            if (static_cast<uint8_t>(range.virtualReg) == regIdx) {
              score += 1.0f;
            }
            if ((range.end - range.start) < 5 && regIdx < 8) {
              score += 0.3f;
            }
            if (score > bestScore) {
              bestScore = score;
              allocatedReg = regIdx;
            }
          }
        }

        if (allocatedReg != 0xFF) {
          range.physicalReg = allocatedReg;
          range.isSpilled = false;
          regAvailable[allocatedReg] = false;
          regLastUsed[allocatedReg] = range.end;
          regPriority[allocatedReg] = range.priority;
          active.push_back(&range);

          spdlog::trace("Allocated physical register {} to virtual register {} "
                        "(range {}-{}, priority {:.2f})",
                        allocatedReg, static_cast<int>(range.virtualReg),
                        range.start, range.end, range.priority);
        } else {
          LiveRange *spillCandidate = nullptr;
          float lowestScore = std::numeric_limits<float>::max();

          for (auto *activeRange : active) {
            float spillScore = activeRange->priority;
            if (activeRange->end - currentTime < 3) {
              spillScore *= 0.3f;
            }
            spillScore += activeRange->accessCount * 0.1f;
            if (activeRange->isDestination) {
              spillScore *= 1.2f;
            }
            spillScore -= activeRange->interferenceCount * 0.05f;

            if (spillScore < lowestScore) {
              lowestScore = spillScore;
              spillCandidate = activeRange;
            }
          }

          float currentSpillScore = range.priority + range.accessCount * 0.1f;
          if (range.isDestination) {
            currentSpillScore *= 1.2f;
          }

          if (spillCandidate && lowestScore < currentSpillScore) {
            spillCandidate->isSpilled = true;
            spillCandidate->spillSlot = spillCount++;
            uint8_t freedReg = spillCandidate->physicalReg;
            spillCandidate->physicalReg = 0xFF;

            range.physicalReg = freedReg;
            range.isSpilled = false;
            regLastUsed[freedReg] = range.end;
            regPriority[freedReg] = range.priority;

            active.erase(
                std::remove(active.begin(), active.end(), spillCandidate),
                active.end());
            active.push_back(&range);

            spdlog::debug(
                "Spilled virtual register {} (priority {:.2f}) to allocate "
                "register {} to virtual register {} (priority {:.2f})",
                static_cast<int>(spillCandidate->virtualReg),
                spillCandidate->priority, freedReg,
                static_cast<int>(range.virtualReg), range.priority);
            allAllocated = false;
          } else {
            range.isSpilled = true;
            range.spillSlot = spillCount++;
            range.physicalReg = 0xFF;

            spdlog::debug(
                "Spilled virtual register {} (no suitable candidates, "
                "priority {:.2f})",
                static_cast<int>(range.virtualReg), range.priority);
            allAllocated = false;
          }
        }
      }

      uint32_t totalRanges = liveRanges.size();
      uint32_t allocatedRanges = totalRanges - spillCount;
      float spillRate = totalRanges > 0 ? static_cast<float>(spillCount) /
                                              totalRanges * 100.0f
                                        : 0.0f;

      spdlog::info("Register allocation complete: {}/{} ranges allocated, {} "
                   "spilled ({:.1f}% spill rate), max pressure: {}/{}",
                   allocatedRanges, totalRanges, spillCount, spillRate,
                   maxActiveRanges, USABLE_REGS);

      for (const auto &range : liveRanges) {
        if (range.priority >= 2.0f) {
          if (range.isSpilled) {
            spdlog::debug("High-priority range SPILLED: register {} -> slot {}",
                          static_cast<int>(range.virtualReg), range.spillSlot);
          } else {
            spdlog::debug(
                "High-priority range allocated: register {} -> physical {}",
                static_cast<int>(range.virtualReg), range.physicalReg);
          }
        }
      }

      return allAllocated;

    } catch (const std::exception &e) {
      spdlog::error("Linear scan register allocation failed: {}", e.what());
      return false;
    }
  }

  void X86_64JITCompiler::GenerateSpillCode(const LiveRange &range,
                                            std::vector<uint8_t> &code) {
    if (!range.isSpilled) {
      return;
    }

    uint32_t spillOffset = SPILL_AREA_OFFSET + (range.spillSlot * 8);

    if (range.physicalReg != 0xFF) {
      code.push_back(0x48);
      code.push_back(0x89);
      uint8_t modrm = 0x84 | (range.physicalReg << 3);
      code.push_back(modrm);
      code.push_back(0x24);
      for (int i = 0; i < 4; ++i) {
        code.push_back(static_cast<uint8_t>(spillOffset >> (i * 8)));
      }
      spdlog::debug("Generated spill code for vreg {} to slot {}",
                    static_cast<int>(range.virtualReg), range.spillSlot);
    }

    code.push_back(0x48);
    code.push_back(0x8B);
    uint8_t modrm = 0x84 | (range.physicalReg << 3);
    code.push_back(modrm);
    code.push_back(0x24);
    for (int i = 0; i < 4; ++i) {
      code.push_back(static_cast<uint8_t>(spillOffset >> (i * 8)));
    }
    spdlog::debug("Generated reload code for vreg {} from slot {}",
                  static_cast<int>(range.virtualReg), range.spillSlot);
  }

  void X86_64JITCompiler::EmitInstruction(const DecodedInstruction &instr,
                                          std::vector<uint8_t> &code) {
    switch (instr.instType) {
    case InstructionType::Mov:
      EmitMovInstruction(instr, code);
      break;
    case InstructionType::Add:
      EmitAddInstruction(instr, code);
      break;
    case InstructionType::Sub:
      EmitSubInstruction(instr, code);
      break;
    case InstructionType::Push:
      EmitPushInstruction(instr, code);
      break;
    case InstructionType::Pop:
      EmitPopInstruction(instr, code);
      break;
    case InstructionType::Mul:
      EmitMulInstruction(instr, code);
      break;
    case InstructionType::Imul:
      EmitImulInstruction(instr, code);
      break;
    case InstructionType::Div:
      EmitDivInstruction(instr, code);
      break;
    case InstructionType::Idiv:
      EmitIdivInstruction(instr, code);
      break;
    case InstructionType::Inc:
      EmitIncInstruction(instr, code);
      break;
    case InstructionType::Dec:
      EmitDecInstruction(instr, code);
      break;
    case InstructionType::Neg:
      EmitNegInstruction(instr, code);
      break;
    case InstructionType::Cmp:
      EmitCmpInstruction(instr, code);
      break;
    case InstructionType::Adc:
      EmitAdcInstruction(instr, code);
      break;
    case InstructionType::Sbb:
      EmitSbbInstruction(instr, code);
      break;
    case InstructionType::And:
      EmitAndInstruction(instr, code);
      break;
    case InstructionType::Or:
      EmitOrInstruction(instr, code);
      break;
    case InstructionType::Xor:
      EmitXorInstruction(instr, code);
      break;
    case InstructionType::Not:
      EmitNotInstruction(instr, code);
      break;
    case InstructionType::Test:
      EmitTestInstruction(instr, code);
      break;
    case InstructionType::Jmp:
      EmitJmpInstruction(instr, code);
      break;
    case InstructionType::Call:
      EmitCallInstruction(instr, code);
      break;
    case InstructionType::Ret:
      EmitRetInstruction(instr, code);
      break;
    case InstructionType::Jcc:
      EmitJccInstruction(instr, code);
      break;
    case InstructionType::Movsx:
      EmitMovsxInstruction(instr, code);
      break;
    case InstructionType::Movzx:
      EmitMovzxInstruction(instr, code);
      break;
    case InstructionType::Xchg:
      EmitXchgInstruction(instr, code);
      break;
    case InstructionType::Lea:
      EmitLeaInstruction(instr, code);
      break;
    case InstructionType::Movaps:
      EmitMovapsInstruction(instr, code);
      break;
    case InstructionType::Addps:
      EmitAddpsInstruction(instr, code);
      break;
    case InstructionType::Subps:
      EmitSubpsInstruction(instr, code);
      break;
    case InstructionType::Mulps:
      EmitMulpsInstruction(instr, code);
      break;
    default:
      spdlog::error("Unsupported instruction type: {}",
                    static_cast<int>(instr.instType));
      throw std::runtime_error("Unknown instruction type");
    }
  }

  void X86_64JITCompiler::EmitMovInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // CRITICAL: Add bounds check for operand access
    if (instr.operandCount < 2) {
      spdlog::error("MOV instruction requires at least 2 operands, got {}", instr.operandCount);
      return;
    }
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t rex = 0x48;
      if (static_cast<uint8_t>(instr.operands[0].reg) >= 8 ||
          static_cast<uint8_t>(instr.operands[1].reg) >= 8) {
        rex |= (static_cast<uint8_t>(instr.operands[0].reg) >= 8 ? 0x41 : 0x00);
        rex |= (static_cast<uint8_t>(instr.operands[1].reg) >= 8 ? 0x44 : 0x00);
      }
      code.push_back(rex);
      code.push_back(0x89);
      uint8_t modrm =
          0xC0 | ((static_cast<uint8_t>(instr.operands[1].reg) & 0x7) << 3) |
          (static_cast<uint8_t>(instr.operands[0].reg) & 0x7);
      code.push_back(modrm);
    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {
      uint8_t rex = 0x48;
      if (static_cast<uint8_t>(instr.operands[0].reg) >= 8) {
        rex |= 0x41;
      }
      code.push_back(rex);
      code.push_back(0xC7);
      uint8_t modrm =
          0xC0 | (static_cast<uint8_t>(instr.operands[0].reg) & 0x7);
      code.push_back(modrm);
      uint64_t imm = instr.operands[1].immediate;
      for (int i = 0; i < 8; ++i) {
        code.push_back(static_cast<uint8_t>(imm >> (i * 8)));
      }
    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::MEMORY ||
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::MEMORY) {
      const bool store_to_mem =
          (instr.operands[0].type == DecodedInstruction::Operand::Type::MEMORY);
      const auto &mem_op = store_to_mem ? instr.operands[0] : instr.operands[1];
      const auto &reg_op = store_to_mem ? instr.operands[1] : instr.operands[0];

      if (reg_op.type != DecodedInstruction::Operand::Type::REGISTER) {
        spdlog::error(
            "Unsupported MOV with memory operand: non-register second operand");
        throw std::runtime_error("Invalid MOV operands");
      }

      const auto &mem = mem_op.memory;
      uint8_t rex = 0x48;

      if (static_cast<uint8_t>(reg_op.reg) >= 8)
        rex |= 0x44;
      if (mem.index != Register::NONE && static_cast<uint8_t>(mem.index) >= 8)
        rex |= 0x42;
      if (mem.base != Register::NONE && static_cast<uint8_t>(mem.base) >= 8)
        rex |= 0x41;

      code.push_back(rex);
      code.push_back(store_to_mem ? 0x89 : 0x8B);

      uint8_t mod = 0, rm = 0;
      uint8_t reg_field = static_cast<uint8_t>(reg_op.reg) & 0x7;
      bool has_sib = false;

      if (mem.base == Register::NONE) {
        mod = 0b00;
        rm = 0b100;
        has_sib = true;
      } else {
        rm = static_cast<uint8_t>(mem.base) & 0x7;
        if (mem.displacement == 0 && rm != 0b101) {
          mod = 0b00;
        } else if (mem.displacement >= -128 && mem.displacement <= 127) {
          mod = 0b01;
        } else {
          mod = 0b10;
        }
        if (rm == 0b100)
          has_sib = true;
      }

      if (mem.index != Register::NONE) {
        has_sib = true;
        rm = 0b100;
      }

      code.push_back((mod << 6) | (reg_field << 3) | rm);

      if (has_sib) {
        uint8_t scale_bits = 0;
        switch (mem.scale) {
        case 1:
          scale_bits = 0;
          break;
        case 2:
          scale_bits = 1;
          break;
        case 4:
          scale_bits = 2;
          break;
        case 8:
          scale_bits = 3;
          break;
        default:
          scale_bits = 0;
        }
        uint8_t index_bits = (mem.index != Register::NONE)
                                 ? (static_cast<uint8_t>(mem.index) & 0x7)
                                 : 0b100;
        uint8_t base_bits = (mem.base != Register::NONE)
                                ? (static_cast<uint8_t>(mem.base) & 0x7)
                                : 0b101;
        code.push_back((scale_bits << 6) | (index_bits << 3) | base_bits);
      }

      if (mod == 0b01) {
        code.push_back(static_cast<uint8_t>(mem.displacement));
      } else if (mod == 0b10 ||
                 (mod == 0b00 && has_sib &&
                  ((mem.base == Register::NONE) ||
                   ((static_cast<uint8_t>(mem.base) & 0x7) == 0b101)))) {
        int32_t disp = mem.displacement;
        code.insert(code.end(), reinterpret_cast<uint8_t *>(&disp),
                    reinterpret_cast<uint8_t *>(&disp) + 4);
      }
    } else {
      spdlog::error("Unsupported MOV operand combination");
      throw std::runtime_error("Invalid MOV operands");
    }
  }

  void X86_64JITCompiler::EmitAddInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // CRITICAL: Add bounds check for operand access
    if (instr.operandCount < 2) {
      spdlog::error("ADD instruction requires at least 2 operands, got {}", instr.operandCount);
      return;
    }
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t rex = 0x48;
      if (static_cast<uint8_t>(instr.operands[0].reg) >= 8 ||
          static_cast<uint8_t>(instr.operands[1].reg) >= 8) {
        rex |= (static_cast<uint8_t>(instr.operands[0].reg) >= 8 ? 0x41 : 0x00);
        rex |= (static_cast<uint8_t>(instr.operands[1].reg) >= 8 ? 0x44 : 0x00);
      }
      code.push_back(rex);
      code.push_back(0x01);
      uint8_t modrm =
          0xC0 | ((static_cast<uint8_t>(instr.operands[1].reg) & 0x7) << 3) |
          (static_cast<uint8_t>(instr.operands[0].reg) & 0x7);
      code.push_back(modrm);
    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {
      uint8_t rex = 0x48;
      if (static_cast<uint8_t>(instr.operands[0].reg) >= 8) {
        rex |= 0x41;
      }
      code.push_back(rex);
      code.push_back(0x81);
      uint8_t modrm =
          0xC0 | (static_cast<uint8_t>(instr.operands[0].reg) & 0x7);
      code.push_back(modrm);
      uint32_t imm = static_cast<uint32_t>(instr.operands[1].immediate);
      for (int i = 0; i < 4; ++i) {
        code.push_back(static_cast<uint8_t>(imm >> (i * 8)));
      }
    } else {
      spdlog::error("Unsupported ADD operand combination");
      throw std::runtime_error("Invalid ADD operands");
    }
  }

  void X86_64JITCompiler::EmitSubInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t rex = 0x48;
      if (static_cast<uint8_t>(instr.operands[0].reg) >= 8 ||
          static_cast<uint8_t>(instr.operands[1].reg) >= 8) {
        rex |= (static_cast<uint8_t>(instr.operands[0].reg) >= 8 ? 0x41 : 0x00);
        rex |= (static_cast<uint8_t>(instr.operands[1].reg) >= 8 ? 0x44 : 0x00);
      }
      code.push_back(rex);
      code.push_back(0x29);
      uint8_t modrm =
          0xC0 | ((static_cast<uint8_t>(instr.operands[1].reg) & 0x7) << 3) |
          (static_cast<uint8_t>(instr.operands[0].reg) & 0x7);
      code.push_back(modrm);
    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {
      uint8_t rex = 0x48;
      if (static_cast<uint8_t>(instr.operands[0].reg) >= 8) {
        rex |= 0x41;
      }
      code.push_back(rex);
      code.push_back(0x81);
      uint8_t modrm =
          0xE8 | (static_cast<uint8_t>(instr.operands[0].reg) & 0x7);
      code.push_back(modrm);
      uint32_t imm = static_cast<uint32_t>(instr.operands[1].immediate);
      for (int i = 0; i < 4; ++i) {
        code.push_back(static_cast<uint8_t>(imm >> (i * 8)));
      }
    } else {
      spdlog::error("Unsupported SUB operand combination");
      throw std::runtime_error("Invalid SUB operands");
    }
  }

  void X86_64JITCompiler::EmitMulInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // MUL instruction - unsigned multiply with RAX
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      // For simplicity, emit a call to a helper function
      // In a full implementation, this would emit native x86-64 MUL instruction
      code.push_back(0x48); // REX.W
      code.push_back(0xB8); // MOV RAX, imm64 (helper function address)

      // Placeholder for helper function address
      for (int i = 0; i < 8; ++i) {
        code.push_back(0x00);
      }

      code.push_back(0xFF); // CALL RAX
      code.push_back(0xD0);
    } else {
      spdlog::error("Unsupported MUL operand type");
      throw std::runtime_error("Invalid MUL operand");
    }
  }

  void X86_64JITCompiler::EmitImulInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
    // IMUL instruction - signed multiply
    if (instr.operandCount == 1) {
      // Single operand IMUL (with RAX)
      if (instr.operands[0].type ==
          DecodedInstruction::Operand::Type::REGISTER) {
        uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

        // Emit call to helper function for complex IMUL logic
        code.push_back(0x48); // REX.W
        code.push_back(0xB8); // MOV RAX, imm64

        // Placeholder for helper function address
        for (int i = 0; i < 8; ++i) {
          code.push_back(0x00);
        }

        code.push_back(0xFF); // CALL RAX
        code.push_back(0xD0);
      }
    } else if (instr.operandCount == 2) {
      // Two operand IMUL (reg = reg * imm/reg)
      if (instr.operands[0].type ==
              DecodedInstruction::Operand::Type::REGISTER &&
          instr.operands[1].type ==
              DecodedInstruction::Operand::Type::IMMEDIATE) {

        uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
        int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

        // IMUL reg, imm32 - 0x69 /r id
        code.push_back(0x48); // REX.W for 64-bit
        code.push_back(0x69); // IMUL opcode
        code.push_back(0xC0 | (reg & 0x7) | ((reg & 0x7) << 3)); // ModR/M

        // Immediate value (32-bit)
        for (int i = 0; i < 4; ++i) {
          code.push_back((imm >> (8 * i)) & 0xFF);
        }
      }
    }
  }

  void X86_64JITCompiler::EmitDivInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // DIV instruction - unsigned divide
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      // Emit call to helper function for division logic and exception handling
      code.push_back(0x48); // REX.W
      code.push_back(0xB8); // MOV RAX, imm64

      // Placeholder for helper function address
      for (int i = 0; i < 8; ++i) {
        code.push_back(0x00);
      }

      code.push_back(0xFF); // CALL RAX
      code.push_back(0xD0);
    } else {
      spdlog::error("Unsupported DIV operand type");
      throw std::runtime_error("Invalid DIV operand");
    }
  }

  void X86_64JITCompiler::EmitIdivInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
    // IDIV instruction - signed divide
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      // Emit call to helper function for signed division logic
      code.push_back(0x48); // REX.W
      code.push_back(0xB8); // MOV RAX, imm64

      // Placeholder for helper function address
      for (int i = 0; i < 8; ++i) {
        code.push_back(0x00);
      }

      code.push_back(0xFF); // CALL RAX
      code.push_back(0xD0);
    } else {
      spdlog::error("Unsupported IDIV operand type");
      throw std::runtime_error("Invalid IDIV operand");
    }
  }

  void X86_64JITCompiler::EmitIncInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // INC instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      if (reg < 8) {
        // INC r64 - REX.W + FF /0
        code.push_back(0x48);       // REX.W
        code.push_back(0xFF);       // INC opcode
        code.push_back(0xC0 + reg); // ModR/M for register
      } else {
        // Extended register (R8-R15)
        code.push_back(0x49);             // REX.WB
        code.push_back(0xFF);             // INC opcode
        code.push_back(0xC0 + (reg - 8)); // ModR/M for extended register
      }
    } else {
      spdlog::error("Unsupported INC operand type");
      throw std::runtime_error("Invalid INC operand");
    }
  }

  void X86_64JITCompiler::EmitDecInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // DEC instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      if (reg < 8) {
        // DEC r64 - REX.W + FF /1
        code.push_back(0x48);       // REX.W
        code.push_back(0xFF);       // DEC opcode
        code.push_back(0xC8 + reg); // ModR/M for register (reg field = 1)
      } else {
        // Extended register (R8-R15)
        code.push_back(0x49);             // REX.WB
        code.push_back(0xFF);             // DEC opcode
        code.push_back(0xC8 + (reg - 8)); // ModR/M for extended register
      }
    } else {
      spdlog::error("Unsupported DEC operand type");
      throw std::runtime_error("Invalid DEC operand");
    }
  }

  void X86_64JITCompiler::EmitNegInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // NEG instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      if (reg < 8) {
        // NEG r64 - REX.W + F7 /3
        code.push_back(0x48);       // REX.W
        code.push_back(0xF7);       // NEG opcode
        code.push_back(0xD8 + reg); // ModR/M for register (reg field = 3)
      } else {
        // Extended register (R8-R15)
        code.push_back(0x49);             // REX.WB
        code.push_back(0xF7);             // NEG opcode
        code.push_back(0xD8 + (reg - 8)); // ModR/M for extended register
      }
    } else {
      spdlog::error("Unsupported NEG operand type");
      throw std::runtime_error("Invalid NEG operand");
    }
  }

  void X86_64JITCompiler::EmitCmpInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // CMP instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // CMP r64, r64 - REX.W + 39 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x39);                                      // CMP opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // CMP r64, imm32 - REX.W + 81 /7 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0x81);               // CMP opcode
      code.push_back(0xF8 + (reg & 0x7)); // ModR/M (reg field = 7)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported CMP operand combination");
      throw std::runtime_error("Invalid CMP operands");
    }
  }

  void X86_64JITCompiler::EmitAdcInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // ADC instruction - add with carry
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // ADC r64, r64 - REX.W + 11 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x11);                                      // ADC opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // ADC r64, imm32 - REX.W + 81 /2 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0x81);               // ADC opcode
      code.push_back(0xD0 + (reg & 0x7)); // ModR/M (reg field = 2)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported ADC operand combination");
      throw std::runtime_error("Invalid ADC operands");
    }
  }

  void X86_64JITCompiler::EmitSbbInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // SBB instruction - subtract with borrow
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // SBB r64, r64 - REX.W + 19 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x19);                                      // SBB opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // SBB r64, imm32 - REX.W + 81 /3 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0x81);               // SBB opcode
      code.push_back(0xD8 + (reg & 0x7)); // ModR/M (reg field = 3)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported SBB operand combination");
      throw std::runtime_error("Invalid SBB operands");
    }
  }

  void X86_64JITCompiler::EmitAndInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // AND instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // AND r64, r64 - REX.W + 21 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x21);                                      // AND opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // AND r64, imm32 - REX.W + 81 /4 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0x81);               // AND opcode
      code.push_back(0xE0 + (reg & 0x7)); // ModR/M (reg field = 4)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported AND operand combination");
      throw std::runtime_error("Invalid AND operands");
    }
  }

  void X86_64JITCompiler::EmitOrInstruction(const DecodedInstruction &instr,
                                            std::vector<uint8_t> &code) {
    // OR instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // OR r64, r64 - REX.W + 09 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x09);                                      // OR opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // OR r64, imm32 - REX.W + 81 /1 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0x81);               // OR opcode
      code.push_back(0xC8 + (reg & 0x7)); // ModR/M (reg field = 1)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported OR operand combination");
      throw std::runtime_error("Invalid OR operands");
    }
  }

  void X86_64JITCompiler::EmitXorInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // XOR instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // XOR r64, r64 - REX.W + 31 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x31);                                      // XOR opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // XOR r64, imm32 - REX.W + 81 /6 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0x81);               // XOR opcode
      code.push_back(0xF0 + (reg & 0x7)); // ModR/M (reg field = 6)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported XOR operand combination");
      throw std::runtime_error("Invalid XOR operands");
    }
  }

  void X86_64JITCompiler::EmitNotInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // NOT instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      if (reg < 8) {
        // NOT r64 - REX.W + F7 /2
        code.push_back(0x48);       // REX.W
        code.push_back(0xF7);       // NOT opcode
        code.push_back(0xD0 + reg); // ModR/M for register (reg field = 2)
      } else {
        // Extended register (R8-R15)
        code.push_back(0x49);             // REX.WB
        code.push_back(0xF7);             // NOT opcode
        code.push_back(0xD0 + (reg - 8)); // ModR/M for extended register
      }
    } else {
      spdlog::error("Unsupported NOT operand type");
      throw std::runtime_error("Invalid NOT operand");
    }
  }

  void X86_64JITCompiler::EmitTestInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
    // TEST instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // TEST r64, r64 - REX.W + 85 /r
      uint8_t rex = 0x48; // REX.W
      if (reg1 >= 8)
        rex |= 0x01; // REX.B
      if (reg2 >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x85);                                      // TEST opcode
      code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M

    } else if (instr.operands[0].type ==
                   DecodedInstruction::Operand::Type::REGISTER &&
               instr.operands[1].type ==
                   DecodedInstruction::Operand::Type::IMMEDIATE) {

      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);
      int32_t imm = static_cast<int32_t>(instr.operands[1].immediate);

      // TEST r64, imm32 - REX.W + F7 /0 id
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0xF7);               // TEST opcode
      code.push_back(0xC0 + (reg & 0x7)); // ModR/M (reg field = 0)

      // Immediate value (32-bit)
      for (int i = 0; i < 4; ++i) {
        code.push_back((imm >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported TEST operand combination");
      throw std::runtime_error("Invalid TEST operands");
    }
  }

  void X86_64JITCompiler::EmitJmpInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // JMP instruction
    if (instr.operands[0].type ==
        DecodedInstruction::Operand::Type::IMMEDIATE) {
      // Direct jump with 32-bit relative offset
      int32_t offset = static_cast<int32_t>(instr.operands[0].immediate);

      // JMP rel32 - E9 cd
      code.push_back(0xE9);

      // 32-bit relative offset
      for (int i = 0; i < 4; ++i) {
        code.push_back((offset >> (8 * i)) & 0xFF);
      }
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::REGISTER) {
      // Indirect jump through register
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      // JMP r64 - REX.W + FF /4
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0xFF);
      code.push_back(0xE0 + (reg & 0x7)); // ModR/M (reg field = 4)
    } else {
      spdlog::error("Unsupported JMP operand type");
      throw std::runtime_error("Invalid JMP operand");
    }
  }

  void X86_64JITCompiler::EmitCallInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
    // CALL instruction
    if (instr.operands[0].type ==
        DecodedInstruction::Operand::Type::IMMEDIATE) {
      // Direct call with 32-bit relative offset
      int32_t offset = static_cast<int32_t>(instr.operands[0].immediate);

      // CALL rel32 - E8 cd
      code.push_back(0xE8);

      // 32-bit relative offset
      for (int i = 0; i < 4; ++i) {
        code.push_back((offset >> (8 * i)) & 0xFF);
      }
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::REGISTER) {
      // Indirect call through register
      uint8_t reg = static_cast<uint8_t>(instr.operands[0].reg);

      // CALL r64 - REX.W + FF /2
      uint8_t rex = 0x48; // REX.W
      if (reg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);
      code.push_back(0xFF);
      code.push_back(0xD0 + (reg & 0x7)); // ModR/M (reg field = 2)
    } else {
      spdlog::error("Unsupported CALL operand type");
      throw std::runtime_error("Invalid CALL operand");
    }
  }

  void X86_64JITCompiler::EmitRetInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // RET instruction
    if (instr.operandCount == 0) {
      // RET (near return) - C3
      code.push_back(0xC3);
    } else if (instr.operands[0].type ==
               DecodedInstruction::Operand::Type::IMMEDIATE) {
      // RET imm16 (near return with stack adjustment) - C2 iw
      uint16_t stackAdjust = static_cast<uint16_t>(instr.operands[0].immediate);

      code.push_back(0xC2);
      code.push_back(stackAdjust & 0xFF);
      code.push_back((stackAdjust >> 8) & 0xFF);
    } else {
      spdlog::error("Unsupported RET operand type");
      throw std::runtime_error("Invalid RET operand");
    }
  }

  void X86_64JITCompiler::EmitJccInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // Generic conditional jump - use condition code
    if (instr.operands[0].type ==
        DecodedInstruction::Operand::Type::IMMEDIATE) {
      int32_t offset = static_cast<int32_t>(instr.operands[0].immediate);

      // Jcc rel32 - 0F 8x cd (where x is condition code)
      code.push_back(0x0F);
      code.push_back(0x80 + instr.conditionCode);

      // 32-bit relative offset
      for (int i = 0; i < 4; ++i) {
        code.push_back((offset >> (8 * i)) & 0xFF);
      }
    } else {
      spdlog::error("Unsupported Jcc operand type");
      throw std::runtime_error("Invalid Jcc operand");
    }
  }

  void X86_64JITCompiler::EmitMovsxInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    // MOVSX instruction - move with sign extension
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t srcReg = static_cast<uint8_t>(instr.operands[1].reg);
      uint8_t srcSize = instr.operands[1].size;

      // REX prefix for 64-bit operation
      uint8_t rex = 0x48; // REX.W
      if (destReg >= 8)
        rex |= 0x04; // REX.R
      if (srcReg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);

      if (srcSize == 8) {
        // MOVSX r64, r8 - 0F BE /r
        code.push_back(0x0F);
        code.push_back(0xBE);
      } else if (srcSize == 16) {
        // MOVSX r64, r16 - 0F BF /r
        code.push_back(0x0F);
        code.push_back(0xBF);
      } else if (srcSize == 32) {
        // MOVSXD r64, r32 - 63 /r
        code.push_back(0x63);
      }

      code.push_back(0xC0 | ((destReg & 0x7) << 3) | (srcReg & 0x7)); // ModR/M
    } else {
      spdlog::error("Unsupported MOVSX operand combination");
      throw std::runtime_error("Invalid MOVSX operands");
    }
  }

  void X86_64JITCompiler::EmitMovzxInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    // MOVZX instruction - move with zero extension
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t srcReg = static_cast<uint8_t>(instr.operands[1].reg);
      uint8_t srcSize = instr.operands[1].size;

      // REX prefix for 64-bit operation
      uint8_t rex = 0x48; // REX.W
      if (destReg >= 8)
        rex |= 0x04; // REX.R
      if (srcReg >= 8)
        rex |= 0x01; // REX.B

      code.push_back(rex);

      if (srcSize == 8) {
        // MOVZX r64, r8 - 0F B6 /r
        code.push_back(0x0F);
        code.push_back(0xB6);
      } else if (srcSize == 16) {
        // MOVZX r64, r16 - 0F B7 /r
        code.push_back(0x0F);
        code.push_back(0xB7);
      }
      // Note: There's no MOVZX for 32->64, use regular MOV (32-bit ops zero
      // upper 32 bits)

      code.push_back(0xC0 | ((destReg & 0x7) << 3) | (srcReg & 0x7)); // ModR/M
    } else {
      spdlog::error("Unsupported MOVZX operand combination");
      throw std::runtime_error("Invalid MOVZX operands");
    }
  }

  void X86_64JITCompiler::EmitXchgInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
    // XCHG instruction
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::REGISTER) {

      uint8_t reg1 = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t reg2 = static_cast<uint8_t>(instr.operands[1].reg);

      // Special case: XCHG RAX, reg or XCHG reg, RAX (single byte encoding)
      if (reg1 == 0) { // RAX
        // XCHG RAX, r64 - REX.W + 90+rd
        uint8_t rex = 0x48; // REX.W
        if (reg2 >= 8)
          rex |= 0x01; // REX.B
        code.push_back(rex);
        code.push_back(0x90 + (reg2 & 0x7));
      } else if (reg2 == 0) { // RAX
        // XCHG r64, RAX - REX.W + 90+rd
        uint8_t rex = 0x48; // REX.W
        if (reg1 >= 8)
          rex |= 0x01; // REX.B
        code.push_back(rex);
        code.push_back(0x90 + (reg1 & 0x7));
      } else {
        // XCHG r64, r64 - REX.W + 87 /r
        uint8_t rex = 0x48; // REX.W
        if (reg1 >= 8)
          rex |= 0x01; // REX.B
        if (reg2 >= 8)
          rex |= 0x04; // REX.R

        code.push_back(rex);
        code.push_back(0x87); // XCHG opcode
        code.push_back(0xC0 | ((reg2 & 0x7) << 3) | (reg1 & 0x7)); // ModR/M
      }
    } else {
      spdlog::error("Unsupported XCHG operand combination");
      throw std::runtime_error("Invalid XCHG operands");
    }
  }

  void X86_64JITCompiler::EmitLeaInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    // LEA instruction - load effective address
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::REGISTER &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::MEMORY) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);

      // LEA r64, m - REX.W + 8D /r
      uint8_t rex = 0x48; // REX.W
      if (destReg >= 8)
        rex |= 0x04; // REX.R

      code.push_back(rex);
      code.push_back(0x8D); // LEA opcode

      // For simplicity, emit a basic ModR/M byte
      // In a full implementation, this would need to encode the full memory
      // operand
      code.push_back(0x00 |
                     ((destReg & 0x7) << 3)); // ModR/M with memory operand

      // Note: This is a simplified implementation
      // A full implementation would need to encode SIB bytes, displacements,
      // etc.
    } else {
      spdlog::error("Unsupported LEA operand combination");
      throw std::runtime_error("Invalid LEA operands");
    }
  }

  void X86_64JITCompiler::EmitMovapsInstruction(const DecodedInstruction &instr,
                                                std::vector<uint8_t> &code) {
    // MOVAPS instruction - move aligned packed single-precision
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::XMM &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t srcReg = static_cast<uint8_t>(instr.operands[1].reg);

      // MOVAPS xmm1, xmm2 - 0F 28 /r
      uint8_t rex = 0x40; // REX prefix (may be needed for extended registers)
      if (destReg >= 8)
        rex |= 0x04; // REX.R
      if (srcReg >= 8)
        rex |= 0x01; // REX.B

      if (rex != 0x40)
        code.push_back(rex);

      code.push_back(0x0F);
      code.push_back(0x28);
      code.push_back(0xC0 | ((destReg & 0x7) << 3) | (srcReg & 0x7)); // ModR/M
    } else {
      spdlog::error("Unsupported MOVAPS operand combination");
      throw std::runtime_error("Invalid MOVAPS operands");
    }
  }

  void X86_64JITCompiler::EmitAddpsInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    // ADDPS instruction - add packed single-precision
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::XMM &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t srcReg = static_cast<uint8_t>(instr.operands[1].reg);

      // ADDPS xmm1, xmm2 - 0F 58 /r
      uint8_t rex = 0x40; // REX prefix (may be needed for extended registers)
      if (destReg >= 8)
        rex |= 0x04; // REX.R
      if (srcReg >= 8)
        rex |= 0x01; // REX.B

      if (rex != 0x40)
        code.push_back(rex);

      code.push_back(0x0F);
      code.push_back(0x58);
      code.push_back(0xC0 | ((destReg & 0x7) << 3) | (srcReg & 0x7)); // ModR/M
    } else {
      spdlog::error("Unsupported ADDPS operand combination");
      throw std::runtime_error("Invalid ADDPS operands");
    }
  }

  void X86_64JITCompiler::EmitSubpsInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    // SUBPS instruction - subtract packed single-precision
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::XMM &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t srcReg = static_cast<uint8_t>(instr.operands[1].reg);

      // SUBPS xmm1, xmm2 - 0F 5C /r
      uint8_t rex = 0x40; // REX prefix (may be needed for extended registers)
      if (destReg >= 8)
        rex |= 0x04; // REX.R
      if (srcReg >= 8)
        rex |= 0x01; // REX.B

      if (rex != 0x40)
        code.push_back(rex);

      code.push_back(0x0F);
      code.push_back(0x5C);
      code.push_back(0xC0 | ((destReg & 0x7) << 3) | (srcReg & 0x7)); // ModR/M
    } else {
      spdlog::error("Unsupported SUBPS operand combination");
      throw std::runtime_error("Invalid SUBPS operands");
    }
  }

  void X86_64JITCompiler::EmitMulpsInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    // MULPS instruction - multiply packed single-precision
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::XMM &&
        instr.operands[1].type == DecodedInstruction::Operand::Type::XMM) {

      uint8_t destReg = static_cast<uint8_t>(instr.operands[0].reg);
      uint8_t srcReg = static_cast<uint8_t>(instr.operands[1].reg);

      // MULPS xmm1, xmm2 - 0F 59 /r
      uint8_t rex = 0x40; // REX prefix (may be needed for extended registers)
      if (destReg >= 8)
        rex |= 0x04; // REX.R
      if (srcReg >= 8)
        rex |= 0x01; // REX.B

      if (rex != 0x40)
        code.push_back(rex);

      code.push_back(0x0F);
      code.push_back(0x59);
      code.push_back(0xC0 | ((destReg & 0x7) << 3) | (srcReg & 0x7)); // ModR/M
    } else {
      spdlog::error("Unsupported MULPS operand combination");
      throw std::runtime_error("Invalid MULPS operands");
    }
  }

  bool X86_64JITCompiler::IsJumpInstruction(uint8_t opcode) {
    if (opcode == 0xE9 || opcode == 0xEB)
      return true;
    if (opcode >= 0x70 && opcode <= 0x7F)
      return true;
    if (opcode == 0xE2 || opcode == 0xE0 || opcode == 0xE1)
      return true;
    return false;
  }

  void X86_64JITCompiler::OptimizeBlock(std::vector<uint8_t> & code) {
    try {
      if (code.empty()) {
        spdlog::warn("OptimizeBlock: Empty code block, nothing to optimize");
        return;
      }

      // Peephole optimization: Remove redundant MOV instructions
      std::vector<uint8_t> optimizedCode;
      optimizedCode.reserve(code.size());
      size_t i = 0;
      while (i < code.size()) {
        // Look for MOV reg, reg (e.g., MOV RAX, RAX)
        if (i + 2 < code.size() && code[i] == 0x48 && code[i + 1] == 0x89) {
          uint8_t modrm = code[i + 2];
          if ((modrm & 0xC0) == 0xC0 && ((modrm >> 3) & 0x7) == (modrm & 0x7)) {
            spdlog::trace("Optimized out redundant MOV reg, reg at offset {}",
                          i);
            i += 3;
            continue;
          }
        }
        // Look for consecutive PUSH/POP of the same register
        if (i + 1 < code.size() && (code[i] >= 0x50 && code[i] <= 0x57) &&
            code[i + 1] == 0x58 + (code[i] - 0x50)) {
          spdlog::trace("Optimized out PUSH/POP pair at offset {}", i);
          i += 2;
          continue;
        }
        optimizedCode.push_back(code[i]);
        i++;
      }

      // Instruction fusion: Combine ADD/SUB with immediate if possible
      i = 0;
      std::vector<uint8_t> fusedCode;
      fusedCode.reserve(optimizedCode.size());
      while (i < optimizedCode.size()) {
        // Look for ADD reg, imm followed by SUB reg, imm
        if (i + 7 < optimizedCode.size() && optimizedCode[i] == 0x48 &&
            optimizedCode[i + 1] == 0x81 &&
            (optimizedCode[i + 2] & 0xF8) == 0xC0 &&
            optimizedCode[i + 7] == 0x48 && optimizedCode[i + 8] == 0x81 &&
            (optimizedCode[i + 9] & 0xF8) == 0xE8 &&
            (optimizedCode[i + 2] & 0x7) == (optimizedCode[i + 9] & 0x7)) {
          uint32_t addImm =
              *reinterpret_cast<uint32_t *>(&optimizedCode[i + 3]);
          uint32_t subImm =
              *reinterpret_cast<uint32_t *>(&optimizedCode[i + 10]);
          int32_t netImm =
              static_cast<int32_t>(addImm) - static_cast<int32_t>(subImm);
          if (netImm == 0) {
            spdlog::trace("Fused ADD/SUB pair to no-op at offset {}", i);
            i += 14;
            continue;
          } else {
            uint8_t rex = 0x48;
            if ((optimizedCode[i + 2] & 0x7) >= 8)
              rex |= 0x41;
            fusedCode.push_back(rex);
            fusedCode.push_back(0x81);
            fusedCode.push_back(netImm >= 0
                                    ? 0xC0 | (optimizedCode[i + 2] & 0x7)
                                    : 0xE8 | (optimizedCode[i + 2] & 0x7));
            for (int j = 0; j < 4; ++j) {
              fusedCode.push_back(
                  static_cast<uint8_t>(std::abs(netImm) >> (j * 8)));
            }
            spdlog::trace("Fused ADD/SUB pair to single {} imm at offset {}",
                          netImm >= 0 ? "ADD" : "SUB", i);
            i += 14;
            continue;
          }
        }
        fusedCode.push_back(optimizedCode[i]);
        i++;
      }

      code.swap(fusedCode);

      // Additional optimization: Remove redundant NOPs
      size_t originalSize = code.size();
      code.erase(std::remove(code.begin(), code.end(), 0x90), code.end());
      if (code.size() < originalSize) {
        spdlog::trace("Removed {} redundant NOPs", originalSize - code.size());
      }

      spdlog::trace("Optimized JIT block: original size={}, optimized size={}",
                    originalSize, code.size());
    } catch (const std::exception &e) {
      spdlog::error("OptimizeBlock failed: {}", e.what());
    }
  }

  void X86_64JITCompiler::FixupRelativeAddresses(uint8_t *code, uint64_t size) {
    try {
      if (!code || size < 5) {
        spdlog::warn("FixupRelativeAddresses: Invalid code pointer or size {}",
                     size);
        return;
      }

      for (uint64_t i = 0; i < size - 4; ++i) {
        if (code[i] == 0xE8) { // CALL instruction
          void *placeholder = *reinterpret_cast<void **>(&code[i + 1]);
          uint64_t callAddr = reinterpret_cast<uint64_t>(&code[i + 5]);
          uint64_t targetAddr = reinterpret_cast<uint64_t>(placeholder);

          // Validate target address
          if (targetAddr == 0 || targetAddr == UINT64_MAX) {
            spdlog::warn("Invalid call target 0x{:x} at offset {}", targetAddr,
                         i);
            continue;
          }

          int32_t offset = static_cast<int32_t>(targetAddr - callAddr);
          *reinterpret_cast<int32_t *>(&code[i + 1]) = offset;
          spdlog::trace(
              "Fixed CALL at offset {} (addr 0x{:x}) to target 0x{:x}, "
              "offset 0x{:x}",
              i, callAddr, targetAddr, offset);
        } else if (IsJumpInstruction(code[i])) { // JMP or Jcc
          if (code[i] == 0xE9 || (code[i] >= 0x70 && code[i] <= 0x7F)) {
            uint32_t offsetSize =
                (code[i] == 0xEB || (code[i] >= 0x70 && code[i] <= 0x7F)) ? 1
                                                                          : 4;
            if (i + offsetSize >= size) {
              spdlog::warn("Incomplete jump instruction at offset {}", i);
              continue;
            }
            void *placeholder = offsetSize == 1
                                    ? reinterpret_cast<void *>(
                                          static_cast<uint64_t>(code[i + 1]))
                                    : *reinterpret_cast<void **>(&code[i + 1]);
            uint64_t jumpAddr =
                reinterpret_cast<uint64_t>(&code[i + 1 + offsetSize]);
            uint64_t targetAddr = reinterpret_cast<uint64_t>(placeholder);

            if (targetAddr == 0 || targetAddr == UINT64_MAX) {
              spdlog::warn("Invalid jump target 0x{:x} at offset {}",
                           targetAddr, i);
              continue;
            }

            int32_t offset = static_cast<int32_t>(targetAddr - jumpAddr);
            if (offsetSize == 1 && (offset < -128 || offset > 127)) {
              spdlog::warn(
                  "Jump offset {} out of range for short jump at offset {}",
                  offset, i);
              continue;
            }

            if (offsetSize == 1) {
              code[i + 1] = static_cast<uint8_t>(offset);
            } else {
              *reinterpret_cast<int32_t *>(&code[i + 1]) = offset;
            }
            spdlog::trace(
                "Fixed {} at offset {} (addr 0x{:x}) to target 0x{:x}, "
                "offset 0x{:x}",
                code[i] == 0xE9 ? "JMP" : "Jcc", i, jumpAddr, targetAddr,
                offset);
          }
        }
      }
    } catch (const std::exception &e) {
      spdlog::error("FixupRelativeAddresses failed: {}", e.what());
    }
  }

  void X86_64JITCompiler::SaveState(std::ostream & out) const {
    try {
      JIT_LOCK(m_cacheMutex);
      auto start = std::chrono::steady_clock::now();

      // CRITICAL FIX: Add stream validation before writing
      if (!out.good()) {
        spdlog::error("JIT SaveState: output stream is not in good state");
        return;
      }

      // Add a version number for future compatibility
      uint32_t version = 1;
      out.write(reinterpret_cast<const char *>(&version), sizeof(version));
      if (!out.good()) {
        spdlog::error("JIT SaveState: failed to write version");
        return;
      }

      uint64_t blockCount = m_compiledBlocks.size();
      out.write(reinterpret_cast<const char *>(&blockCount),
                sizeof(blockCount));
      if (!out.good()) {
        spdlog::error("JIT SaveState: failed to write block count");
        return;
      }

      for (const auto &entry : m_compiledBlocks) {
        const uint64_t pc = entry.first;
        const auto &block = entry.second;

        out.write(reinterpret_cast<const char *>(&pc), sizeof(pc));
        out.write(reinterpret_cast<const char *>(&block.size),
                  sizeof(block.size));
        // CRITICAL FIX: Check if code pointer is valid before writing
        if (block.code && block.size > 0) {
          out.write(reinterpret_cast<const char *>(block.code), block.size);
        } else {
          spdlog::warn("JIT SaveState: Skipping invalid block code at 0x{:x}",
                       pc);
          // Write dummy data or throw error if this is critical
          std::vector<uint8_t> dummy(block.size, 0);
          out.write(reinterpret_cast<const char *>(dummy.data()), block.size);
        }

        out.write(reinterpret_cast<const char *>(&block.lastUsed),
                  sizeof(block.lastUsed));
        out.write(reinterpret_cast<const char *>(&block.branchCount),
                  sizeof(block.branchCount));
        out.write(reinterpret_cast<const char *>(&block.executionCount),
                  sizeof(block.executionCount));
        out.write(reinterpret_cast<const char *>(&block.totalCycles),
                  sizeof(block.totalCycles));
        out.write(reinterpret_cast<const char *>(&block.isHotPath),
                  sizeof(block.isHotPath));
        out.write(reinterpret_cast<const char *>(&block.compilationTimeUs),
                  sizeof(block.compilationTimeUs));

        // Ensure all writes were successful for this block
        if (!out.good()) {
          spdlog::error("JIT SaveState: failed to write block data for 0x{:x}",
                        pc);
          throw JITCompileException("Stream write error during SaveState");
        }
      }

      out.write(reinterpret_cast<const char *>(&m_totalCodeSize),
                sizeof(m_totalCodeSize));
      out.write(reinterpret_cast<const char *>(&m_cycleCount),
                sizeof(m_cycleCount));
      out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

      if (!out.good()) {
        spdlog::error("JIT SaveState: failed to write global stats");
        throw JITCompileException("Stream write error during SaveState");
      }

      auto end = std::chrono::steady_clock::now();
      uint64_t saveTimeUs =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("JIT state saved: {} blocks, time={}us", blockCount,
                   saveTimeUs);
    } catch (const std::exception &e) {
      spdlog::error("SaveState failed: {}", e.what());
    }
  }

  void X86_64JITCompiler::LoadState(std::istream & in) {
    try {
      JIT_LOCK(m_cacheMutex);
      auto start = std::chrono::steady_clock::now();

      // CRITICAL FIX: Add stream validation before reading
      if (!in.good()) {
        spdlog::error("JIT LoadState: input stream is not in good state");
        throw JITCompileException("Input stream not in good state");
      }

      ClearCache(); // Clear existing cache before loading

      uint32_t version;
      in.read(reinterpret_cast<char *>(&version), sizeof(version));
      if (!in.good()) {
        spdlog::error("JIT LoadState: failed to read version");
        throw JITCompileException(
            "Stream read error during LoadState (version)");
      }
      if (version != 1) {
        spdlog::error("Unsupported JIT state version: {}", version);
        throw JITCompileException("Unsupported JIT state version");
      }

      uint64_t blockCount;
      in.read(reinterpret_cast<char *>(&blockCount), sizeof(blockCount));
      if (!in.good()) {
        spdlog::error("JIT LoadState: failed to read block count");
        throw JITCompileException(
            "Stream read error during LoadState (block count)");
      }

      for (uint64_t i = 0; i < blockCount; ++i) {
        uint64_t pc;
        in.read(reinterpret_cast<char *>(&pc), sizeof(pc));
        if (!in.good()) {
          spdlog::error("JIT LoadState: failed to read PC for block {}", i);
          throw JITCompileException("Stream read error during LoadState (PC)");
        }

        x86_64::X86_64JITCompiler::BlockInfo block;
        in.read(reinterpret_cast<char *>(&block.size), sizeof(block.size));
        if (!in.good()) {
          spdlog::error("JIT LoadState: failed to read size for block 0x{:x}",
                        pc);
          throw JITCompileException(
              "Stream read error during LoadState (size)");
        }

        if (block.size == 0) {
          spdlog::warn("JIT LoadState: Encountered zero-sized block at 0x{:x}",
                       pc);
          block.code = nullptr; // Ensure code is null for zero size
        } else {
          uint8_t *code = AllocateExecutableMemory(block.size);
          if (!code) {
            spdlog::error(
                "LoadState: Failed to allocate memory for block at 0x{:x}", pc);
            // Attempt to skip remaining data for this block to continue loading
            in.seekg(block.size, std::ios_base::cur); // Skip code data
            in.seekg(sizeof(block.lastUsed) + sizeof(block.branchCount) +
                         sizeof(block.executionCount) +
                         sizeof(block.totalCycles) + sizeof(block.isHotPath) +
                         sizeof(block.compilationTimeUs),
                     std::ios_base::cur); // Skip remaining BlockInfo data
            continue;                     // Skip to next block
          }
          in.read(reinterpret_cast<char *>(code), block.size);
          if (!in.good()) {
            spdlog::error("JIT LoadState: failed to read code for block 0x{:x}",
                          pc);
            FreeExecutableMemory(code, block.size); // Clean up allocated memory
            throw JITCompileException(
                "Stream read error during LoadState (code)");
          }
          block.code = code;
        }

        in.read(reinterpret_cast<char *>(&block.lastUsed),
                sizeof(block.lastUsed));
        in.read(reinterpret_cast<char *>(&block.branchCount),
                sizeof(block.branchCount));
        in.read(reinterpret_cast<char *>(&block.executionCount),
                sizeof(block.executionCount));
        in.read(reinterpret_cast<char *>(&block.totalCycles),
                sizeof(block.totalCycles));
        in.read(reinterpret_cast<char *>(&block.isHotPath),
                sizeof(block.isHotPath));
        in.read(reinterpret_cast<char *>(&block.compilationTimeUs),
                sizeof(block.compilationTimeUs));

        if (!in.good()) {
          spdlog::error(
              "JIT LoadState: failed to read block metadata for 0x{:x}", pc);
          if (block.code)
            FreeExecutableMemory(block.code, block.size);
          throw JITCompileException(
              "Stream read error during LoadState (metadata)");
        }

        m_compiledBlocks[pc] = block;
        m_totalCodeSize += block.size;
      }

      in.read(reinterpret_cast<char *>(&m_totalCodeSize),
              sizeof(m_totalCodeSize));
      in.read(reinterpret_cast<char *>(&m_cycleCount), sizeof(m_cycleCount));
      in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

      if (!in.good()) {
        spdlog::error("JIT LoadState: failed to read global stats");
        throw JITCompileException(
            "Stream read error during LoadState (global stats)");
      }

      auto end = std::chrono::steady_clock::now();
      uint64_t loadTimeUs =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("JIT state loaded: {} blocks, time={}us", blockCount,
                   loadTimeUs);
    } catch (const std::exception &e) {
      spdlog::error("LoadState failed: {}", e.what());
      ClearCache(); // Ensure cache is cleared on any load error
    }
  }

  // Placeholder implementations for new methods declared in .h
  // These would be filled with actual logic for advanced optimizations and
  // emission
  void X86_64JITCompiler::DeadCodeElimination(std::vector<uint8_t> & code) {
    spdlog::debug("DeadCodeElimination pass (placeholder)");
    // Implementation for dead code elimination
  }

  void X86_64JITCompiler::CommonSubexpressionElimination(std::vector<uint8_t> &
                                                         code) {
    spdlog::debug("CommonSubexpressionElimination pass (placeholder)");
    // Implementation for common subexpression elimination
  }

  void X86_64JITCompiler::ConstantFoldingAndPropagation(std::vector<uint8_t> &
                                                        code) {
    spdlog::debug("ConstantFoldingAndPropagation pass (placeholder)");
    // Implementation for constant folding and propagation
  }

  void X86_64JITCompiler::InstructionCombining(std::vector<uint8_t> & code) {
    spdlog::debug("InstructionCombining pass (placeholder)");
    // Implementation for instruction combining
  }

  void X86_64JITCompiler::LoopOptimizations(std::vector<uint8_t> & code) {
    spdlog::debug("LoopOptimizations pass (placeholder)");
    // Implementation for loop optimizations (unrolling, invariant motion, etc.)
  }

  void X86_64JITCompiler::ControlFlowOptimizations(std::vector<uint8_t> &
                                                   code) {
    spdlog::debug("ControlFlowOptimizations pass (placeholder)");
    // Implementation for control flow optimizations (jump threading, branch
    // prediction hints)
  }

  bool X86_64JITCompiler::ShouldUseOptimization(uint64_t pc,
                                                OptimizationLevel level) {
    // Placeholder logic: determine if a block should be optimized at a given
    // level This would typically involve checking profiling data (e.g.,
    // execution count, hot path status)
    auto it = m_compiledBlocks.find(pc);
    if (it != m_compiledBlocks.end()) {
      if (it->second.isHotPath &&
          it->second.optimizationLevel < static_cast<uint8_t>(level)) {
        return true;
      }
    }
    return false;
  }

  X86_64JITCompiler::OptimizationLevel
  X86_64JITCompiler::DetermineOptimizationLevel(uint64_t pc) {
    // Placeholder logic: determine the appropriate optimization level for a
    // block
    auto it = m_compiledBlocks.find(pc);
    if (it != m_compiledBlocks.end() && it->second.isHotPath) {
      if (it->second.executionCount > 5000)
        return OptimizationLevel::Maximum;
      if (it->second.executionCount > 1000)
        return OptimizationLevel::Aggressive;
      if (it->second.executionCount > 100)
        return OptimizationLevel::Basic;
    }
    return OptimizationLevel::None;
  }

  void X86_64JITCompiler::ApplyOptimizationPasses(std::vector<uint8_t> & code,
                                                  OptimizationLevel level) {
    spdlog::debug("Applying optimization passes up to level {}",
                  static_cast<int>(level));
    // Example:
    if (level >= OptimizationLevel::Basic) {
      DeadCodeElimination(code);
      ConstantFoldingAndPropagation(code);
    }
    if (level >= OptimizationLevel::Aggressive) {
      CommonSubexpressionElimination(code);
      InstructionCombining(code);
    }
    if (level >= OptimizationLevel::Maximum) {
      LoopOptimizations(code);
      ControlFlowOptimizations(code);
    }
  }

  void X86_64JITCompiler::EmitArithmeticInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitArithmeticInstruction (placeholder)");
    // Implement based on instruction type (ADD, SUB, MUL, DIV, etc.)
  }

  void X86_64JITCompiler::EmitMemoryInstruction(const DecodedInstruction &instr,
                                                std::vector<uint8_t> &code) {
    spdlog::debug("EmitMemoryInstruction (placeholder)");
    // Implement based on instruction type (MOV, PUSH, POP, LEA, etc. involving
    // memory)
  }

  void X86_64JITCompiler::EmitControlFlowInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitControlFlowInstruction (placeholder)");
    // Implement based on instruction type (JMP, CALL, RET, Jcc)
  }

  void X86_64JITCompiler::EmitSIMDInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
    spdlog::debug("EmitSIMDInstruction (placeholder)");
    // Implement based on SIMD instruction type (ADDPS, MULPS, etc.)
  }

  void X86_64JITCompiler::EmitLogicalInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitLogicalInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitBitwiseInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitBitwiseInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitStringInstruction(const DecodedInstruction &instr,
                                                std::vector<uint8_t> &code) {
    spdlog::debug("EmitStringInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitStackInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    spdlog::debug("EmitStackInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitCompareInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitCompareInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitShiftInstruction(const DecodedInstruction &instr,
                                               std::vector<uint8_t> &code) {
    spdlog::debug("EmitShiftInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitConditionalInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitConditionalInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitSystemInstruction(const DecodedInstruction &instr,
                                                std::vector<uint8_t> &code) {
    spdlog::debug("EmitSystemInstruction (placeholder)");
  }

  void X86_64JITCompiler::EmitPackedArithmeticInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitPackedArithmeticInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitPackedLogicalInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitPackedLogicalInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitPackedCompareInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitPackedCompareInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitPackedMoveInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitPackedMoveInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitPackedConversionInstruction(
      const DecodedInstruction &instr, std::vector<uint8_t> &code) {
    spdlog::debug("EmitPackedConversionInstruction (placeholder)");
  }
  void X86_64JITCompiler::EmitAVXInstruction(const DecodedInstruction &instr,
                                             std::vector<uint8_t> &code) {
    spdlog::debug("EmitAVXInstruction (placeholder)");
  }

  void X86_64JITCompiler::EmitREXPrefix(bool w, bool r, bool x, bool b,
                                        std::vector<uint8_t> &code) {
    uint8_t rex = 0x40;
    if (w)
      rex |= 0x08;
    if (r)
      rex |= 0x04;
    if (x)
      rex |= 0x02;
    if (b)
      rex |= 0x01;
    if (rex != 0x40) { // Only emit if not default REX prefix
      code.push_back(rex);
    }
  }

  void X86_64JITCompiler::EmitVEXPrefix(
      uint8_t len, uint8_t pp, uint8_t mmmmm, bool w, bool r, bool x, bool b,
      uint8_t vvvv, std::vector<uint8_t> &code) {
    // VEX prefix encoding is complex and depends on instruction. This is a
    // simplified placeholder. VEX 2-byte: C5 / VEX 3-byte: C4
    if (len == 2) {
      code.push_back(0xC5);
      uint8_t byte2 = 0;
      if (!r)
        byte2 |= 0x80;      // R' (inverted REX.R)
      byte2 |= (vvvv << 3); // vvvv
      byte2 |= pp;          // pp
      byte2 |= mmmmm;       // mmmmm (ignored for 2-byte VEX)
      code.push_back(byte2);
    } else if (len == 3) {
      code.push_back(0xC4);
      uint8_t byte2 = 0;
      if (!r)
        byte2 |= 0x80; // R'
      if (!x)
        byte2 |= 0x40; // X'
      if (!b)
        byte2 |= 0x20; // B'
      byte2 |= mmmmm;  // mmmmm
      code.push_back(byte2);
      uint8_t byte3 = 0;
      if (w)
        byte3 |= 0x80;      // W
      byte3 |= (vvvv << 3); // vvvv
      byte3 |= pp;          // pp
      code.push_back(byte3);
    } else {
      spdlog::error("Invalid VEX prefix length: {}", len);
    }
  }

  uint8_t X86_64JITCompiler::EncodeModRM(uint8_t mod, uint8_t reg, uint8_t rm) {
    return (mod << 6) | (reg << 3) | rm;
  }

  uint8_t X86_64JITCompiler::EncodeSIB(uint8_t scale, uint8_t index,
                                       uint8_t base) {
    return (scale << 6) | (index << 3) | base;
  }

  void X86_64JITCompiler::EmitAddressingMode(
      const DecodedInstruction::Operand &operand, uint8_t regField,
      std::vector<uint8_t> &code) {
    spdlog::debug("EmitAddressingMode (placeholder)");
    // This would be a complex function handling all ModR/M and SIB byte
    // combinations
  }

  void X86_64JITCompiler::EmitImmediate(uint64_t immediate, uint8_t size,
                                        std::vector<uint8_t> &code) {
    for (uint8_t i = 0; i < size; ++i) {
      code.push_back(static_cast<uint8_t>((immediate >> (i * 8)) & 0xFF));
    }
  }

  bool X86_64JITCompiler::ValidateInstructionOperands(
      const DecodedInstruction &instr) {
    spdlog::debug("ValidateInstructionOperands (placeholder)");
    // Basic validation: check operand count, types, sizes
    return true;
  }

  bool X86_64JITCompiler::ValidateSIMDOperands(
      const DecodedInstruction &instr) {
    spdlog::debug("ValidateSIMDOperands (placeholder)");
    // Check if operands are XMM/YMM registers, memory, correct sizes
    return true;
  }

  bool X86_64JITCompiler::ValidateRegisterRange(Register reg, bool isXMM) {
    spdlog::debug("ValidateRegisterRange (placeholder)");
    if (isXMM) {
      return static_cast<uint8_t>(reg) < 16; // XMM0-XMM15
    } else {
      return static_cast<uint8_t>(reg) < 16; // RAX-R15
    }
  }

  std::vector<X86_64JITCompiler::LoopInfo> X86_64JITCompiler::DetectLoops(
      const std::vector<uint8_t> &code) {
    spdlog::debug("DetectLoops (placeholder)");
    return {};
  }

  bool X86_64JITCompiler::ShouldUnrollLoop(const LoopInfo &loop) {
    spdlog::debug("ShouldUnrollLoop (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::UnrollLoop(std::vector<uint8_t> & code,
                                     const LoopInfo &loop) {
    spdlog::debug("UnrollLoop (placeholder)");
    return false;
  }

  size_t X86_64JITCompiler::EstimateLoopTripCount(
      const std::vector<uint8_t> &code, size_t start, size_t end) {
    spdlog::debug("EstimateLoopTripCount (placeholder)");
    return 0;
  }

  bool X86_64JITCompiler::IsCountingLoop(const std::vector<uint8_t> &code,
                                         size_t start, size_t end) {
    spdlog::debug("IsCountingLoop (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::PerformLoopStrengthReduction(
      std::vector<uint8_t> & code, const LoopInfo &loop) {
    spdlog::debug("PerformLoopStrengthReduction (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::PerformLoopInvariantCodeMotion(
      std::vector<uint8_t> & code, const LoopInfo &loop) {
    spdlog::debug("PerformLoopInvariantCodeMotion (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::OptimizeJumpChains(std::vector<uint8_t> & code,
                                             size_t offset) {
    spdlog::debug("OptimizeJumpChains (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::RemoveRedundantJumps(std::vector<uint8_t> & code,
                                               size_t offset) {
    spdlog::debug("RemoveRedundantJumps (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::OptimizeBranchPrediction(std::vector<uint8_t> & code,
                                                   size_t offset) {
    spdlog::debug("OptimizeBranchPrediction (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::OptimizeConditionalMoves(std::vector<uint8_t> & code,
                                                   size_t offset) {
    spdlog::debug("OptimizeConditionalMoves (placeholder)");
    return false;
  }

  size_t X86_64JITCompiler::GetInstructionLength(
      const std::vector<uint8_t> &code, size_t offset) {
    // This would require a full instruction decoder or a lookup table
    // For now, return a dummy value or throw if not implemented
    spdlog::warn("GetInstructionLength is a placeholder and may return "
                 "incorrect values.");
    if (offset >= code.size())
      return 0;
    uint8_t opcode = code[offset];
    // Very basic length estimation for common opcodes
    if (opcode == 0x90)
      return 1; // NOP
    if (opcode == 0x50 || opcode == 0x58)
      return 1;           // PUSH/POP reg
    if (opcode == 0x48) { // REX.W prefix
      if (offset + 1 < code.size()) {
        uint8_t next_opcode = code[offset + 1];
        if (next_opcode == 0x89 || next_opcode == 0x8B)
          return 3; // MOV reg, reg
        if (next_opcode == 0x81 || next_opcode == 0xC7)
          return 7; // ADD/MOV reg, imm32
      }
    }
    if (opcode == 0xE8 || opcode == 0xE9)
      return 5; // CALL/JMP rel32
    if (opcode == 0xEB)
      return 2; // JMP rel8
    if (opcode >= 0x70 && opcode <= 0x7F)
      return 2; // Jcc rel8
    return 1;   // Default to 1 if unknown
  }

  bool X86_64JITCompiler::FoldArithmeticWithZero(std::vector<uint8_t> & code,
                                                 size_t offset) {
    spdlog::debug("FoldArithmeticWithZero (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::FoldMultiplication(std::vector<uint8_t> & code,
                                             size_t offset) {
    spdlog::debug("FoldMultiplication (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::FoldLogicalOperations(std::vector<uint8_t> & code,
                                                size_t offset) {
    spdlog::debug("FoldLogicalOperations (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::PropagateConstants(std::vector<uint8_t> & code,
                                             size_t offset) {
    spdlog::debug("PropagateConstants (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::FoldShiftOperations(std::vector<uint8_t> & code,
                                              size_t offset) {
    spdlog::debug("FoldShiftOperations (placeholder)");
    return false;
  }

  uint64_t X86_64JITCompiler::HashInstruction(const std::vector<uint8_t> &code,
                                              size_t offset, size_t length) {
    spdlog::debug("HashInstruction (placeholder)");
    // Simple hash for demonstration
    uint64_t hash = 0;
    for (size_t i = 0; i < length && (offset + i) < code.size(); ++i) {
      hash = (hash << 5) + hash + code[offset + i];
    }
    return hash;
  }

  bool X86_64JITCompiler::IsLoadInstruction(const std::vector<uint8_t> &code,
                                            size_t offset) {
    spdlog::debug("IsLoadInstruction (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::HasSideEffects(const std::vector<uint8_t> &code,
                                         size_t start, size_t end) {
    spdlog::debug("HasSideEffects (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::IsArithmeticInstruction(
      const std::vector<uint8_t> &code, size_t offset) {
    spdlog::debug("IsArithmeticInstruction (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::ModifiesOperands(const std::vector<uint8_t> &code,
                                           size_t start, size_t end) {
    spdlog::debug("ModifiesOperands (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::CanReplaceWithMove(const std::vector<uint8_t> &code,
                                             size_t current, size_t previous) {
    spdlog::debug("CanReplaceWithMove (placeholder)");
    return false;
  }

  void X86_64JITCompiler::ReplaceWithMove(std::vector<uint8_t> & code,
                                          size_t current, size_t previous) {
    spdlog::debug("ReplaceWithMove (placeholder)");
  }

  void X86_64JITCompiler::InvalidateAffectedEntries(
      std::unordered_map<uint64_t, size_t> & valueTable,
      std::unordered_map<uint64_t, size_t> & lastSeen,
      const std::vector<uint8_t> &code, size_t offset, size_t length) {
    spdlog::debug("InvalidateAffectedEntries (placeholder)");
  }

  bool X86_64JITCompiler::CombineLeaInstructions(std::vector<uint8_t> & code,
                                                 size_t offset) {
    spdlog::debug("CombineLeaInstructions (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::ReduceMultiplicationToShift(
      std::vector<uint8_t> & code, size_t offset) {
    spdlog::debug("ReduceMultiplicationToShift (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::CombineArithmeticOperations(
      std::vector<uint8_t> & code, size_t offset) {
    spdlog::debug("CombineArithmeticOperations (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::CombineMoveOperations(std::vector<uint8_t> & code,
                                                size_t offset) {
    spdlog::debug("CombineMoveOperations (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::ReduceDivisionToShift(std::vector<uint8_t> & code,
                                                size_t offset) {
    spdlog::debug("ReduceDivisionToShift (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::CombineCompareAndJump(std::vector<uint8_t> & code,
                                                size_t offset) {
    spdlog::debug("CombineCompareAndJump (placeholder)");
    return false;
  }

  bool X86_64JITCompiler::ReplaceExpensiveOperations(
      std::vector<uint8_t> & code, size_t offset) {
    spdlog::debug("ReplaceExpensiveOperations (placeholder)");
    return false;
  }

// Missing JIT compiler method implementations
void X86_64JITCompiler::EmitPushInstruction(const DecodedInstruction& instr, std::vector<uint8_t>& code) {
  if (instr.operandCount >= 1) {
    const auto* operand = SafeGetOperand(instr, 0);
    if (!operand) return;
    
    if (operand->type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(operand->reg);
      if (reg < 8) {
        code.push_back(0x50 + reg); // PUSH r64
      } else {
        code.push_back(0x41); // REX.B
        code.push_back(0x50 + (reg - 8));
      }
    }
  }
}

void X86_64JITCompiler::EmitPopInstruction(const DecodedInstruction& instr, std::vector<uint8_t>& code) {
  if (instr.operandCount >= 1) {
    const auto* operand = SafeGetOperand(instr, 0);
    if (!operand) return;
    
    if (operand->type == DecodedInstruction::Operand::Type::REGISTER) {
      uint8_t reg = static_cast<uint8_t>(operand->reg);
      if (reg < 8) {
        code.push_back(0x58 + reg); // POP r64
      } else {
        code.push_back(0x41); // REX.B
        code.push_back(0x58 + (reg - 8));
      }
    }
  }
}

} // namespace x86_64